using UnityEngine;
using TMPro;
using System.Text;

/// <summary>
/// Handles TextMeshPro font fallback for missing Unicode characters
/// Automatically replaces missing characters with safe alternatives
/// </summary>
public class TextMeshProFallbackHandler : MonoBehaviour
{
    [Header("Fallback Settings")]
    [SerializeField] private bool enableAutoFallback = true;
    [SerializeField] private string fallbackCharacter = "?";
    [SerializeField] private bool logMissingCharacters = true;
    
    private TextMeshProUGUI textComponent;
    private string originalText;
    
    void Awake()
    {
        textComponent = GetComponent<TextMeshProUGUI>();
        if (textComponent == null)
        {
            Debug.LogWarning("TextMeshProFallbackHandler: No TextMeshProUGUI component found on this GameObject.", this);
            enabled = false;
            return;
        }
        
        // Store original text
        originalText = textComponent.text;
    }
    
    void Start()
    {
        if (enableAutoFallback && textComponent != null)
        {
            ProcessTextForFallback();
        }
    }
    
    /// <summary>
    /// Processes the text and replaces missing Unicode characters with fallback characters
    /// </summary>
    public void ProcessTextForFallback()
    {
        if (textComponent == null || string.IsNullOrEmpty(textComponent.text))
            return;
            
        string processedText = ProcessStringForFallback(textComponent.text);
        
        if (processedText != textComponent.text)
        {
            textComponent.text = processedText;
            if (logMissingCharacters)
            {
                Debug.LogWarning($"TextMeshProFallbackHandler: Replaced missing Unicode characters in text component '{gameObject.name}'.", this);
            }
        }
    }
    
    /// <summary>
    /// Processes a string and replaces problematic Unicode characters
    /// </summary>
    /// <param name="input">Input string to process</param>
    /// <returns>Processed string with fallback characters</returns>
    public string ProcessStringForFallback(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;
            
        StringBuilder sb = new StringBuilder();
        
        foreach (char c in input)
        {
            // Check for problematic Unicode characters
            if (IsProblematicCharacter(c))
            {
                sb.Append(fallbackCharacter);
                if (logMissingCharacters)
                {
                    Debug.LogWarning($"TextMeshProFallbackHandler: Replaced Unicode character \\u{((int)c):X4} with fallback character '{fallbackCharacter}'.", this);
                }
            }
            else
            {
                sb.Append(c);
            }
        }
        
        return sb.ToString();
    }
    
    /// <summary>
    /// Checks if a character is problematic (missing from font or causing issues)
    /// </summary>
    /// <param name="c">Character to check</param>
    /// <returns>True if character should be replaced</returns>
    private bool IsProblematicCharacter(char c)
    {
        int unicodeValue = (int)c;
        
        // Check for the specific problematic character mentioned in the error (\uC637)
        if (unicodeValue == 0xC637)
        {
            return true;
        }
        
        // Check for other potentially problematic Unicode ranges
        // Korean Hangul Syllables (AC00-D7AF) - common source of font issues
        if (unicodeValue >= 0xAC00 && unicodeValue <= 0xD7AF)
        {
            return true;
        }
        
        // Chinese/Japanese characters that might not be in LiberationSans
        if (unicodeValue >= 0x4E00 && unicodeValue <= 0x9FFF) // CJK Unified Ideographs
        {
            return true;
        }
        
        // Other problematic ranges can be added here as needed
        
        return false;
    }
    
    /// <summary>
    /// Updates the text and processes it for fallback characters
    /// </summary>
    /// <param name="newText">New text to set</param>
    public void SetTextWithFallback(string newText)
    {
        if (textComponent == null)
            return;
            
        originalText = newText;
        
        if (enableAutoFallback)
        {
            textComponent.text = ProcessStringForFallback(newText);
        }
        else
        {
            textComponent.text = newText;
        }
    }
    
    /// <summary>
    /// Restores the original text without fallback processing
    /// </summary>
    public void RestoreOriginalText()
    {
        if (textComponent != null && !string.IsNullOrEmpty(originalText))
        {
            textComponent.text = originalText;
        }
    }
    
    /// <summary>
    /// Static utility method to process any string for fallback characters
    /// </summary>
    /// <param name="input">Input string</param>
    /// <param name="fallback">Fallback character to use</param>
    /// <returns>Processed string</returns>
    public static string ProcessStringForFallbackStatic(string input, string fallback = "?")
    {
        if (string.IsNullOrEmpty(input))
            return input;
            
        StringBuilder sb = new StringBuilder();
        
        foreach (char c in input)
        {
            int unicodeValue = (int)c;
            
            // Check for problematic characters
            if (unicodeValue == 0xC637 || // Specific character from error
                (unicodeValue >= 0xAC00 && unicodeValue <= 0xD7AF) || // Korean Hangul
                (unicodeValue >= 0x4E00 && unicodeValue <= 0x9FFF)) // CJK Ideographs
            {
                sb.Append(fallback);
            }
            else
            {
                sb.Append(c);
            }
        }
        
        return sb.ToString();
    }
}
