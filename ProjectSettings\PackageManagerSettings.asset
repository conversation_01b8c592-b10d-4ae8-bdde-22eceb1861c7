%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 13964, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EnablePreReleasePackages: 0
  m_AdvancedSettingsExpanded: 1
  m_ScopedRegistriesSettingsExpanded: 1
  m_SeeAllPackageVersions: 0
  m_DismissPreviewPackagesInUse: 0
  oneTimeWarningShown: 0
  oneTimeDeprecatedPopUpShown: 0
  m_Registries:
  - m_Id: main
    m_Name: 
    m_Url: https://packages.unity.com
    m_Scopes: []
    m_IsDefault: 1
    m_Capabilities: 7
    m_ConfigSource: 0
  - m_Id: scoped:project:package.openupm.com
    m_Name: package.openupm.com
    m_Url: https://package.openupm.com
    m_Scopes:
    - com.ivanmurzak.unity.mcp
    - org.nuget.microsoft.aspnetcore.connections.abstractions
    - org.nuget.microsoft.aspnetcore.http.connections.client
    - org.nuget.microsoft.aspnetcore.http.connections.common
    - org.nuget.microsoft.aspnetcore.signalr.client
    - org.nuget.microsoft.aspnetcore.signalr.client.core
    - org.nuget.microsoft.aspnetcore.signalr.common
    - org.nuget.microsoft.aspnetcore.signalr.protocols.json
    - org.nuget.microsoft.bcl.asyncinterfaces
    - org.nuget.microsoft.bcl.memory
    - org.nuget.microsoft.bcl.timeprovider
    - org.nuget.microsoft.codeanalysis.analyzers
    - org.nuget.microsoft.codeanalysis.common
    - org.nuget.microsoft.codeanalysis.csharp
    - org.nuget.microsoft.extensions.caching.abstractions
    - org.nuget.microsoft.extensions.configuration
    - org.nuget.microsoft.extensions.configuration.abstractions
    - org.nuget.microsoft.extensions.configuration.binder
    - org.nuget.microsoft.extensions.configuration.commandline
    - org.nuget.microsoft.extensions.configuration.environmentvariables
    - org.nuget.microsoft.extensions.configuration.fileextensions
    - org.nuget.microsoft.extensions.configuration.json
    - org.nuget.microsoft.extensions.configuration.usersecrets
    - org.nuget.microsoft.extensions.dependencyinjection
    - org.nuget.microsoft.extensions.dependencyinjection.abstractions
    - org.nuget.microsoft.extensions.diagnostics
    - org.nuget.microsoft.extensions.diagnostics.abstractions
    - org.nuget.microsoft.extensions.features
    - org.nuget.microsoft.extensions.fileproviders.abstractions
    - org.nuget.microsoft.extensions.fileproviders.physical
    - org.nuget.microsoft.extensions.filesystemglobbing
    - org.nuget.microsoft.extensions.hosting
    - org.nuget.microsoft.extensions.hosting.abstractions
    - org.nuget.microsoft.extensions.logging
    - org.nuget.microsoft.extensions.logging.abstractions
    - org.nuget.microsoft.extensions.logging.configuration
    - org.nuget.microsoft.extensions.logging.console
    - org.nuget.microsoft.extensions.logging.debug
    - org.nuget.microsoft.extensions.logging.eventlog
    - org.nuget.microsoft.extensions.logging.eventsource
    - org.nuget.microsoft.extensions.options
    - org.nuget.microsoft.extensions.options.configurationextensions
    - org.nuget.microsoft.extensions.primitives
    - org.nuget.r3
    - org.nuget.system.buffers
    - org.nuget.system.collections.immutable
    - org.nuget.system.componentmodel.annotations
    - org.nuget.system.diagnostics.diagnosticsource
    - org.nuget.system.diagnostics.eventlog
    - org.nuget.system.io.pipelines
    - org.nuget.system.memory
    - org.nuget.system.net.serversentevents
    - org.nuget.system.numerics.vectors
    - org.nuget.system.reflection.metadata
    - org.nuget.system.runtime.compilerservices.unsafe
    - org.nuget.system.security.principal.windows
    - org.nuget.system.text.encoding.codepages
    - org.nuget.system.text.encodings.web
    - org.nuget.system.text.json
    - org.nuget.system.threading.channels
    - org.nuget.system.threading.tasks.extensions
    m_IsDefault: 0
    m_Capabilities: 0
    m_ConfigSource: 4
  m_UserSelectedRegistryName: package.openupm.com
  m_UserAddingNewScopedRegistry: 0
  m_RegistryInfoDraft:
    m_Modified: 0
    m_ErrorMessage: 
    m_UserModificationsInstanceId: -868
    m_OriginalInstanceId: -870
  m_LoadAssets: -1
