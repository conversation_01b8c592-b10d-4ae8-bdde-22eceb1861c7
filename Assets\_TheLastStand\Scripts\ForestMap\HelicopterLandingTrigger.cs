using UnityEngine;
using Mirror;

public class HelicopterLandingTrigger : NetworkBehaviour
{
    [SerializeField] private Transform helicopterTransform;
    [SerializeField] private float landingThresholdHeight = 0.5f;
    [SerializeField] private bool triggerOnlyOnce = true;
    [SerializeField] private float exitSequenceDelay = 1.5f;

    private bool hasTriggeredExit = false;
    private bool isNearGround = false;
    private float timeSinceLanded = 0f;

    [Server]
    private void Update()
    {
        if (!isServer)
            return;

        if (hasTriggeredExit && triggerOnlyOnce)
            return;

        if (helicopterTransform == null)
        {
            Debug.LogWarning("Helicopter transform not assigned to HelicopterLandingTrigger!");
            return;
        }

        // Check if helicopter is near the ground
        isNearGround = Physics.Raycast(helicopterTransform.position, Vector3.down, landingThresholdHeight, Physics.DefaultRaycastLayers, QueryTriggerInteraction.Ignore);

        // Check if helicopter has landed
        if (isNearGround)
        {
            timeSinceLanded += Time.deltaTime;

            // Allow a short delay after landing before triggering the exit sequence
            if (timeSinceLanded >= exitSequenceDelay && !hasTriggeredExit)
            {
                TriggerExitSequence();
            }
        }
        else
        {
            // Reset if helicopter takes off again
            timeSinceLanded = 0f;
        }
    }

    [Server]
    public void TriggerExitSequence()
    {
        if (!isServer)
            return;

        if (hasTriggeredExit && triggerOnlyOnce)
            return;

        Debug.Log("Helicopter has landed, triggering exit sequence");
        
        // Get the helicopter exit manager and start the sequence
        HelicopterExitManager exitManager = HelicopterExitManager.Instance;
        if (exitManager != null)
        {
            exitManager.BeginHelicopterExitSequence();
            hasTriggeredExit = true;
        }
        else
        {
            Debug.LogError("HelicopterExitManager not found in scene!");
        }
    }

    // Alternative approach: Use a collider on the landing zone
    [Server]
    private void OnTriggerEnter(Collider other)
    {
        if (!isServer)
            return;

        // Check if the helicopter has entered the landing zone
        if (helicopterTransform != null && other.transform == helicopterTransform)
        {
            Debug.Log("Helicopter entered landing zone");
            // You could trigger immediately or start checking for proper landing
            // For immediate trigger: TriggerExitSequence();
        }
    }

    // Public method to manually trigger the exit sequence (e.g., from an animation event or another script)
    [Server]
    public void ManualTrigger()
    {
        if (!isServer)
            return;

        TriggerExitSequence();
    }
} 