using UnityEngine;
using UnityEditor;
using TMPro;

/// <summary>
/// Editor utility to help manage TextMeshPro fallback handlers
/// </summary>
public class TextMeshProFallbackUtility : EditorWindow
{
    [MenuItem("Tools/TextMeshPro Fallback/Add Fallback Handlers to Scene")]
    public static void AddFallbackHandlersToScene()
    {
        TextMeshProUGUI[] textComponents = FindObjectsOfType<TextMeshProUGUI>();
        int addedCount = 0;
        
        foreach (TextMeshProUGUI textComponent in textComponents)
        {
            if (textComponent.GetComponent<TextMeshProFallbackHandler>() == null)
            {
                textComponent.gameObject.AddComponent<TextMeshProFallbackHandler>();
                addedCount++;
            }
        }
        
        Debug.Log($"TextMeshProFallbackUtility: Added fallback handlers to {addedCount} TextMeshPro components.");
        
        if (addedCount > 0)
        {
            EditorUtility.SetDirty(UnityEngine.SceneManagement.SceneManager.GetActiveScene().GetRootGameObjects()[0]);
        }
    }
    
    [MenuItem("Tools/TextMeshPro Fallback/Remove All Fallback Handlers")]
    public static void RemoveAllFallbackHandlers()
    {
        TextMeshProFallbackHandler[] handlers = FindObjectsOfType<TextMeshProFallbackHandler>();
        int removedCount = handlers.Length;
        
        foreach (TextMeshProFallbackHandler handler in handlers)
        {
            DestroyImmediate(handler);
        }
        
        Debug.Log($"TextMeshProFallbackUtility: Removed {removedCount} fallback handlers.");
    }
    
    [MenuItem("Tools/TextMeshPro Fallback/Process All Text in Scene")]
    public static void ProcessAllTextInScene()
    {
        TextMeshProFallbackHandler[] handlers = FindObjectsOfType<TextMeshProFallbackHandler>();
        int processedCount = 0;
        
        foreach (TextMeshProFallbackHandler handler in handlers)
        {
            handler.ProcessTextForFallback();
            processedCount++;
        }
        
        Debug.Log($"TextMeshProFallbackUtility: Processed text in {processedCount} fallback handlers.");
    }
    
    [MenuItem("Tools/TextMeshPro Fallback/Find Problematic Characters")]
    public static void FindProblematicCharacters()
    {
        TextMeshProUGUI[] textComponents = FindObjectsOfType<TextMeshProUGUI>();
        int problematicCount = 0;
        
        foreach (TextMeshProUGUI textComponent in textComponents)
        {
            if (!string.IsNullOrEmpty(textComponent.text))
            {
                foreach (char c in textComponent.text)
                {
                    int unicodeValue = (int)c;
                    
                    // Check for the specific problematic character and other ranges
                    if (unicodeValue == 0xC637 || 
                        (unicodeValue >= 0xAC00 && unicodeValue <= 0xD7AF) ||
                        (unicodeValue >= 0x4E00 && unicodeValue <= 0x9FFF))
                    {
                        Debug.LogWarning($"Found problematic character \\u{unicodeValue:X4} in '{textComponent.gameObject.name}': '{textComponent.text}'", textComponent.gameObject);
                        problematicCount++;
                        break; // Only log once per component
                    }
                }
            }
        }
        
        if (problematicCount == 0)
        {
            Debug.Log("TextMeshProFallbackUtility: No problematic characters found in scene.");
        }
        else
        {
            Debug.Log($"TextMeshProFallbackUtility: Found problematic characters in {problematicCount} TextMeshPro components.");
        }
    }
}
