using UnityEngine;
using UnityEngine.UI; // Assuming you might use UI Sliders

public class MainMenu_AudioManager : MonoBehaviour
{
    public static MainMenu_AudioManager instance;

    [Header("Audio Sources")]
    public AudioSource musicAudioSource;
    public AudioSource uiSfxAudioSource;

    [Header("UI Sliders (Optional)")]
    public Slider masterVolumeSlider;
    public Slider musicVolumeSlider;
    public Slider soundFxVolumeSlider;

    [Header("Music Tracks")]
    public AudioClip mainMenuMusic;


    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            Destroy(gameObject); // Ensure only one instance
            return;
        }
    }

    void Start()
    {
        // Ensure AudioSettings instance is ready
        if (AudioSettings.Instance != null)
        {
            ApplyVolumeSettings();
            SetupSliderListeners();
            UpdateSliderValues();

            if (musicAudioSource != null && mainMenuMusic != null)
            {
                musicAudioSource.clip = mainMenuMusic;
                musicAudioSource.loop = true;
                musicAudioSource.Play();
            }
            else
            {
                if (musicAudioSource == null)
                    Debug.LogWarning("MainMenu_AudioManager: MusicAudioSource is not assigned.");
                if (mainMenuMusic == null)
                    Debug.LogWarning("MainMenu_AudioManager: MainMenuMusic AudioClip is not assigned.");
            }
        }
        else
        {
            Debug.LogError("AudioSettings.Instance is not found. Make sure an AudioSettings GameObject exists in the scene.");
        }
    }

    public void ApplyVolumeSettings()
    {
        if (AudioSettings.Instance == null)
        {
            Debug.LogError("MainMenu_AudioManager.ApplyVolumeSettings: AudioSettings.Instance is null!");
            return;
        }

        Debug.Log($"MainMenu_AudioManager.ApplyVolumeSettings: Reading Master={AudioSettings.Instance.masterVolume}, Music={AudioSettings.Instance.musicVolume}, SFX={AudioSettings.Instance.soundFxVolume}");

        if (musicAudioSource != null)
        {
            float newMusicVol = (AudioSettings.Instance.masterVolume / 100f) * (AudioSettings.Instance.musicVolume / 100f);
            Debug.Log($"MainMenu_AudioManager.ApplyVolumeSettings: Calculated musicAudioSource.volume = {newMusicVol}. Current musicAudioSource.volume before apply = {musicAudioSource.volume}");
            musicAudioSource.volume = newMusicVol;
        }
        else { Debug.LogWarning("MainMenu_AudioManager.ApplyVolumeSettings: musicAudioSource is null."); }

        if (uiSfxAudioSource != null)
        {
            float newSfxVol = (AudioSettings.Instance.masterVolume / 100f) * (AudioSettings.Instance.soundFxVolume / 100f);
            Debug.Log($"MainMenu_AudioManager.ApplyVolumeSettings: Calculated uiSfxAudioSource.volume = {newSfxVol}. Current uiSfxAudioSource.volume before apply = {uiSfxAudioSource.volume}");
            uiSfxAudioSource.volume = newSfxVol;
        }
        else { Debug.LogWarning("MainMenu_AudioManager.ApplyVolumeSettings: uiSfxAudioSource is null."); }
    }

    private void SetupSliderListeners()
    {
        if (masterVolumeSlider != null)
        {
            masterVolumeSlider.onValueChanged.AddListener(SetMasterVolume);
        }
        if (musicVolumeSlider != null)
        {
            musicVolumeSlider.onValueChanged.AddListener(SetMusicVolume);
        }
        if (soundFxVolumeSlider != null)
        {
            soundFxVolumeSlider.onValueChanged.AddListener(SetSoundFxVolume);
        }
    }

    private void UpdateSliderValues()
    {
        if (AudioSettings.Instance == null) return;

        Debug.Log("MainMenu_AudioManager.UpdateSliderValues: Updating slider visual values from AudioSettings (0-100) to slider scale (0-1).");

        if (masterVolumeSlider != null)
        {
            masterVolumeSlider.value = AudioSettings.Instance.masterVolume / 100f;
            Debug.Log($"MainMenu_AudioManager.UpdateSliderValues: Master Slider set to {masterVolumeSlider.value} from AS value {AudioSettings.Instance.masterVolume}");
        }
        if (musicVolumeSlider != null)
        {
            musicVolumeSlider.value = AudioSettings.Instance.musicVolume / 100f;
            Debug.Log($"MainMenu_AudioManager.UpdateSliderValues: Music Slider set to {musicVolumeSlider.value} from AS value {AudioSettings.Instance.musicVolume}");
        }
        if (soundFxVolumeSlider != null)
        {
            soundFxVolumeSlider.value = AudioSettings.Instance.soundFxVolume / 100f;
            Debug.Log($"MainMenu_AudioManager.UpdateSliderValues: SFX Slider set to {soundFxVolumeSlider.value} from AS value {AudioSettings.Instance.soundFxVolume}");
        }
    }

    public void SetMasterVolume(float volumeSliderValue)
    {
        if (AudioSettings.Instance != null)
        {
            Debug.Log($"MainMenu_AudioManager.SetMasterVolume: Received slider value: {volumeSliderValue}. Converting to 0-100 scale.");
            AudioSettings.Instance.SetMasterVolume(volumeSliderValue * 100f);
            ApplyVolumeSettings(); // Re-apply all volumes as master affects all
        }
    }

    public void SetMusicVolume(float volumeSliderValue)
    {
        if (AudioSettings.Instance != null)
        {
            Debug.Log($"MainMenu_AudioManager.SetMusicVolume: Received slider value: {volumeSliderValue}. Converting to 0-100 scale.");
            AudioSettings.Instance.SetMusicVolume(volumeSliderValue * 100f);
            ApplyVolumeSettings(); // Apply specific volume change
        }
    }

    public void SetSoundFxVolume(float volumeSliderValue)
    {
        if (AudioSettings.Instance != null)
        {
            Debug.Log($"MainMenu_AudioManager.SetSoundFxVolume: Received slider value: {volumeSliderValue}. Converting to 0-100 scale.");
            AudioSettings.Instance.SetSoundFxVolume(volumeSliderValue * 100f);
            ApplyVolumeSettings(); // Apply specific volume change
        }
    }

    // Example method to play a UI sound
    public void PlayUISound(AudioClip clip)
    {
        if (uiSfxAudioSource != null && clip != null)
        {
            uiSfxAudioSource.PlayOneShot(clip);
        }
    }

    // Ensure volumes are updated if settings change elsewhere while menu is active
    void OnEnable()
    {
        // This could be called if the settings are changed in a sub-menu and then the user returns
        if (AudioSettings.Instance != null)
        {
             ApplyVolumeSettings();
             UpdateSliderValues();
        }
    }
}