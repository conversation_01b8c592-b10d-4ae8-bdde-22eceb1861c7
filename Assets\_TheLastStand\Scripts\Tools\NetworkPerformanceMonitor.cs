using UnityEngine;
using Mirror;
using System.Collections.Generic;

[RequireComponent(typeof(NetworkManager))]
public class NetworkPerformanceMonitor : MonoBehaviour
{
    [Header("Performance Monitoring")]
    [SerializeField] private bool enableMonitoring = true;
    [SerializeField] private float updateInterval = 1.0f;
    [SerializeField] private bool showDebugUI = false;
    
    [Header("Thresholds")]
    [SerializeField] private int maxSyncVarUpdatesPerSecond = 30;
    [SerializeField] private int maxRPCsPerSecond = 20;
    [SerializeField] private float maxNetworkLatency = 100f; // milliseconds
    
    private float lastUpdateTime;
    private int syncVarUpdatesThisSecond;
    private int rpcsThisSecond;
    private List<float> latencyHistory = new List<float>();
    
    // Performance metrics
    public static NetworkPerformanceMonitor Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void Update()
    {
        if (!enableMonitoring) return;
        
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            UpdatePerformanceMetrics();
            lastUpdateTime = Time.time;
        }
    }
    
    private void UpdatePerformanceMetrics()
    {
        // Check SyncVar update rate
        if (syncVarUpdatesThisSecond > maxSyncVarUpdatesPerSecond)
        {
            Debug.LogWarning($"NetworkPerformanceMonitor: High SyncVar update rate detected: {syncVarUpdatesThisSecond}/sec (threshold: {maxSyncVarUpdatesPerSecond}/sec)");
        }
        
        // Check RPC rate
        if (rpcsThisSecond > maxRPCsPerSecond)
        {
            Debug.LogWarning($"NetworkPerformanceMonitor: High RPC rate detected: {rpcsThisSecond}/sec (threshold: {maxRPCsPerSecond}/sec)");
        }
        
        // Check network latency
        if (NetworkClient.active && NetworkTime.rtt > maxNetworkLatency / 1000f)
        {
            Debug.LogWarning($"NetworkPerformanceMonitor: High network latency detected: {NetworkTime.rtt * 1000f:F1}ms (threshold: {maxNetworkLatency}ms)");
        }
        
        // Reset counters
        syncVarUpdatesThisSecond = 0;
        rpcsThisSecond = 0;
        
        // Update latency history
        if (NetworkClient.active)
        {
            latencyHistory.Add((float)(NetworkTime.rtt * 1000f));
            if (latencyHistory.Count > 60) // Keep last 60 seconds
            {
                latencyHistory.RemoveAt(0);
            }
        }
    }
    
    public void RecordSyncVarUpdate()
    {
        if (enableMonitoring)
        {
            syncVarUpdatesThisSecond++;
        }
    }
    
    public void RecordRPC()
    {
        if (enableMonitoring)
        {
            rpcsThisSecond++;
        }
    }
    
    public float GetAverageLatency()
    {
        if (latencyHistory.Count == 0) return 0f;
        
        float sum = 0f;
        foreach (float latency in latencyHistory)
        {
            sum += latency;
        }
        return sum / latencyHistory.Count;
    }
    
    private void OnGUI()
    {
        if (!showDebugUI || !enableMonitoring) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("Network Performance Monitor", GUI.skin.label);
        GUILayout.Space(5);
        
        if (NetworkClient.active)
        {
            GUILayout.Label($"Connection Status: Connected");
            GUILayout.Label($"Current RTT: {NetworkTime.rtt * 1000f:F1}ms");
            GUILayout.Label($"Average RTT: {GetAverageLatency():F1}ms");
            GUILayout.Label($"SyncVar Updates/sec: {syncVarUpdatesThisSecond}");
            GUILayout.Label($"RPCs/sec: {rpcsThisSecond}");
            
            if (LobbyPlayerList.instance != null)
            {
                GUILayout.Label($"Players in Lobby: {LobbyPlayerList.instance.allClients.Count}");
            }
        }
        else
        {
            GUILayout.Label("Connection Status: Disconnected");
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
    
    // Static methods for easy access from other scripts
    public static void LogSyncVarUpdate()
    {
        if (Instance != null)
        {
            Instance.RecordSyncVarUpdate();
        }
    }
    
    public static void LogRPC()
    {
        if (Instance != null)
        {
            Instance.RecordRPC();
        }
    }
    
    // Performance optimization suggestions
    [System.Serializable]
    public class PerformanceReport
    {
        public float averageLatency;
        public int syncVarUpdatesPerSecond;
        public int rpcsPerSecond;
        public List<string> recommendations = new List<string>();
        
        public void GenerateRecommendations()
        {
            recommendations.Clear();
            
            if (syncVarUpdatesPerSecond > 30)
            {
                recommendations.Add("Consider reducing SyncVar update frequency or using [SyncVar] hooks more efficiently");
            }
            
            if (rpcsPerSecond > 20)
            {
                recommendations.Add("Consider batching RPCs or reducing RPC call frequency");
            }
            
            if (averageLatency > 100f)
            {
                recommendations.Add("High latency detected - check network connection or server performance");
            }
            
            if (recommendations.Count == 0)
            {
                recommendations.Add("Network performance is within acceptable ranges");
            }
        }
    }
    
    public PerformanceReport GenerateReport()
    {
        PerformanceReport report = new PerformanceReport
        {
            averageLatency = GetAverageLatency(),
            syncVarUpdatesPerSecond = syncVarUpdatesThisSecond,
            rpcsPerSecond = rpcsThisSecond
        };
        
        report.GenerateRecommendations();
        return report;
    }
}
