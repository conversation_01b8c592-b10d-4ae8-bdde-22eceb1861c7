using UnityEngine;

public class ForestIntroHelicopter : Helicopter
{
    [Header("Forest Intro Specific Settings")]
    [SerializeField] private float specificLandingTime = 3f;


    private Transform currentTargetWaypointTransform;

    public override void Start()
    {
        base.Start();
    }

    public void StartIntroSequence()
    {
        if (this.waypoints == null || this.waypoints.Count == 0)
        {
            enabled = false;
            return;
        }

        if (waypoints.Count > 0) {
            currentTargetWaypointTransform = waypoints[0];
        }
        BeginNavigation(this.waypoints, specificLandingTime);
    }

    public void OnNavigationCompleted() 
    {
    }

    protected void Update() 
    {
        if (currentState == HelicopterState.MovingToWaypoint && activeTargetWaypoint != null)
        {
            Vector3 directionToWaypoint = activeTargetWaypoint.position - transform.position;
            directionToWaypoint.z = 0;

            if (directionToWaypoint.sqrMagnitude > 0.01f)
            {
                directionToWaypoint.Normalize();
                float angle = Mathf.Atan2(directionToWaypoint.y, directionToWaypoint.x) * Mathf.Rad2Deg;
                Quaternion targetRotation = Quaternion.Euler(-90, 0, angle -65);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, Time.deltaTime * rotationSpeed);
            }
        }
    }

    protected void OnWaypointReached(Transform reachedWaypoint)
    {

        int reachedWaypointIndex = -1;
        if (this.waypoints != null)
        {
            reachedWaypointIndex = this.waypoints.IndexOf(reachedWaypoint);
        }

        if (reachedWaypointIndex != -1)
        {
            Debug.Log($"ForestIntroHelicopter: Reached waypoint '{reachedWaypoint.name}' (Index: {reachedWaypointIndex}). Base class will handle progression.", this);
        }
        else
        {
            Debug.LogWarning($"ForestIntroHelicopter: Reached waypoint '{reachedWaypoint.name}', but it was not found in the assigned waypoints list. This could indicate a configuration issue.", this);
        }
    }
}
