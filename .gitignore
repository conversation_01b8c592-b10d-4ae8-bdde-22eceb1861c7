/[Ll]ibrary/
/[Tt]emp/
/[Oo]bj/
/[Bb]uild/
/[Bb]uilds/
/[Ll]ogs/
/[Uu]ser[Ss]ettings/
/[Mm]emoryCaptures/
/[Pp]ackage[Cc]ache/
/[Pp]layer[Dd]ata[Cc]ache/
/[Ss]tate[Cc]ache/
/[Ss]cript[Aa]ssemblies/
/[Ss]earch/
/[Ss]hader[Cc]ache/
/[Ss]plash[Ss]creen[Cc]ache/
/[Tt]emp[Aa]rtifacts/
/[Uu]IElements/
/[Bb]urst[Cc]ache/
/[Bb]uild[Ii]nstructions/
/[Bb]uild[Pp]layer[Dd]ata/
/[Bb]uild[Pp]rofiles/
/[Ee]ncryption[Tt]ransport[Ee]ditor.csproj
/[Ff]izzy[Ss]teamworks.csproj
/[Kk]cp2k.csproj
/[Mm]irror.*.csproj
/[Ss]imple[Ww]eb[Tt]ransport.csproj
/[Tt]elepathy.csproj
/[Uu]nity.[Mm]irror.[Cc]ode[Gg]en.csproj
*.csproj
*.unityproj
*.sln
*.suo
*.tmp
*.user
*.userprefs
*.pidb
*.booproj
*.svd
*.pdb
*.mdb
*.opendb
*.VC.db
*.pidb.meta
*.pdb.meta
*.mdb.meta
sysinfo.txt
crashlytics-build.properties
*.apk
*.aab
*.app
*.unitypackage
~UnityDirMonSyncFile~*
/[Aa]ssets/[Aa]ddressable[Aa]ssets[Dd]ata/*.*.bin*
/[Aa]ssets/[Ss]treamingAssets/aa.meta
/[Aa]ssets/[Ss]treamingAssets/aa/*
/[Aa]ssets/AssetStoreTools*
/[Aa]ssets/Plugins/PlasticSCM*
.vs/
.vscode/
.idea/
.gradle/
ExportedObj/
.consulo/
*.private
*.private.meta
^*.private.[0-9]+$
^*.private.[0-9]+.meta$
.DS_Store*
Thumbs.db
Desktop.ini

# Ignore all files and folders in Assets
/Assets/*
# Except for _TheLastStand folder and its .meta file
!/Assets/_TheLastStand/
!/Assets/_TheLastStand.meta
# And except for everything inside _TheLastStand folder
!/Assets/_TheLastStand/*
!/Assets/_TheLastStand/*/*
!/Assets/_TheLastStand/*/*/*
!/Assets/_TheLastStand/*/*/*/*
!/Assets/_TheLastStand/*/*/*/*/*
# Add more /* patterns if your directory structure is deeper
