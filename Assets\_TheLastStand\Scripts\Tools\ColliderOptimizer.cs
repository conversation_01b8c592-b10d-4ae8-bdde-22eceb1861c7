using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

#if UNITY_EDITOR
[System.Serializable]
public class ColliderOptimizer : EditorWindow
{
    [MenuItem("Tools/Collider Optimizer")]
    public static void ShowWindow()
    {
        GetWindow<ColliderOptimizer>("Collider Optimizer");
    }

    private Vector2 scrollPosition;
    private List<GameObject> problematicObjects = new List<GameObject>();
    private bool autoFix = false;

    private void OnGUI()
    {
        GUILayout.Label("Collider Optimizer", EditorStyles.boldLabel);
        GUILayout.Space(10);

        if (GUILayout.Button("Scan Scene for Problematic Colliders"))
        {
            ScanScene();
        }

        GUILayout.Space(10);
        autoFix = EditorGUILayout.Toggle("Auto-fix issues", autoFix);

        if (problematicObjects.Count > 0)
        {
            GUILayout.Label($"Found {problematicObjects.Count} problematic objects:", EditorStyles.boldLabel);
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            for (int i = 0; i < problematicObjects.Count; i++)
            {
                if (problematicObjects[i] == null) continue;
                
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.ObjectField(problematicObjects[i], typeof(GameObject), true);
                
                if (GUILayout.Button("Fix", GUILayout.Width(50)))
                {
                    FixObject(problematicObjects[i]);
                }
                
                if (GUILayout.Button("Select", GUILayout.Width(60)))
                {
                    Selection.activeGameObject = problematicObjects[i];
                }
                
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndScrollView();
            
            GUILayout.Space(10);
            if (GUILayout.Button("Fix All Issues"))
            {
                FixAllIssues();
            }
        }
    }

    private void ScanScene()
    {
        problematicObjects.Clear();
        
        // Find all colliders in the scene
        Collider[] allColliders = FindObjectsOfType<Collider>();
        
        foreach (Collider collider in allColliders)
        {
            if (IsProblematic(collider))
            {
                problematicObjects.Add(collider.gameObject);
            }
        }
        
        Debug.Log($"Collider Optimizer: Found {problematicObjects.Count} problematic colliders");
    }

    private bool IsProblematic(Collider collider)
    {
        Transform transform = collider.transform;
        Vector3 scale = transform.lossyScale;
        
        // Check for problematic scales
        if (scale.x <= 0.001f || scale.y <= 0.001f || scale.z <= 0.001f)
        {
            Debug.LogWarning($"Extremely small scale detected: {collider.name} - {scale}", collider);
            return true;
        }
        
        if (scale.x == 0 || scale.y == 0 || scale.z == 0)
        {
            Debug.LogWarning($"Zero scale detected: {collider.name} - {scale}", collider);
            return true;
        }
        
        if (scale.x > 50f || scale.y > 50f || scale.z > 50f)
        {
            Debug.LogWarning($"Very large scale detected: {collider.name} - {scale}", collider);
            return true;
        }
        
        // Check for very thin objects (aspect ratio issues)
        float maxScale = Mathf.Max(scale.x, scale.y, scale.z);
        float minScale = Mathf.Min(scale.x, scale.y, scale.z);
        
        if (maxScale / minScale > 1000f)
        {
            Debug.LogWarning($"Extreme aspect ratio detected: {collider.name} - {scale}", collider);
            return true;
        }
        
        // Check for mesh colliders with complex meshes
        if (collider is MeshCollider meshCollider)
        {
            if (meshCollider.sharedMesh != null && meshCollider.sharedMesh.triangles.Length > 10000)
            {
                Debug.LogWarning($"Complex mesh collider detected: {collider.name} - {meshCollider.sharedMesh.triangles.Length / 3} triangles", collider);
                return true;
            }
        }
        
        return false;
    }

    private void FixObject(GameObject obj)
    {
        Collider collider = obj.GetComponent<Collider>();
        if (collider == null) return;
        
        Undo.RecordObject(obj.transform, "Fix Collider Scale");
        
        Vector3 scale = obj.transform.localScale;
        
        // Fix zero or extremely small scales
        if (scale.x <= 0.001f) scale.x = 0.1f;
        if (scale.y <= 0.001f) scale.y = 0.1f;
        if (scale.z <= 0.001f) scale.z = 0.1f;
        
        // Fix extremely large scales
        if (scale.x > 50f) scale.x = 10f;
        if (scale.y > 50f) scale.y = 10f;
        if (scale.z > 50f) scale.z = 10f;
        
        obj.transform.localScale = scale;
        
        // For mesh colliders with complex meshes, suggest using a simpler collider
        if (collider is MeshCollider meshCollider && meshCollider.sharedMesh != null)
        {
            if (meshCollider.sharedMesh.triangles.Length > 10000)
            {
                Debug.LogWarning($"Consider replacing complex MeshCollider on {obj.name} with a simpler BoxCollider or CapsuleCollider for better performance", obj);
            }
        }
        
        EditorUtility.SetDirty(obj);
        Debug.Log($"Fixed collider on {obj.name}", obj);
    }

    private void FixAllIssues()
    {
        foreach (GameObject obj in problematicObjects)
        {
            if (obj != null)
            {
                FixObject(obj);
            }
        }
        
        // Rescan after fixing
        ScanScene();
    }
}
#endif
