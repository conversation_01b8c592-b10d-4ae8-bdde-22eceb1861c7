# Networking Architecture - TheLastStand

## 🌐 Overview

TheLastStand uses **Mirror Networking** as the core multiplayer framework with **Steam** integration for lobby management and matchmaking. The architecture supports up to 4 players in a host-client model.

## 🏗️ Network Architecture

```mermaid
graph TB
    subgraph "Steam Layer"
        SteamAPI[Steam API]
        SteamLobby[Steam Lobby]
        SteamFriends[Steam Friends]
    end
    
    subgraph "Mirror Network Layer"
        NetworkManager[MyNetworkManager]
        NetworkServer[Network Server]
        NetworkClient[Network Client]
    end
    
    subgraph "Game Layer"
        LobbyController[Lobby Controller]
        PlayerList[Lobby Player List]
        MyClient[My Client]
    end
    
    SteamAPI --> NetworkManager
    SteamLobby --> NetworkManager
    NetworkManager --> NetworkServer
    NetworkManager --> NetworkClient
    NetworkServer --> LobbyController
    NetworkClient --> PlayerList
    PlayerList --> MyClient
```

## 🔧 Core Components

### MyNetworkManager
**Location:** `Assets/_TheLastStand/Scripts/Core/MyNetworkManager.cs`

Central network management component that extends Mirror's NetworkManager.

**Key Features:**
- Scene-aware player spawning
- Steam integration coordination
- Player lifecycle management
- Multiplayer/singleplayer mode switching

**Key Methods:**
```csharp
public override void OnServerAddPlayer(NetworkConnectionToClient conn)
public void HandleForestMapSpawning(NetworkConnectionToClient conn)
public void InitializeSpawnedPlayer(NetworkConnectionToClient conn, GameObject player)
public void SetMultiplayer(bool value)
```

### SteamLobby
**Location:** `Assets/_TheLastStand/Scripts/Core/SteamLobby.cs`

Manages Steam lobby operations and matchmaking.

**Key Features:**
- Lobby creation and joining
- Matchmaking with filters
- Host address management
- Lobby data synchronization

**Key Methods:**
```csharp
public void CreateLobby()
public void JoinLobby(CSteamID lobby)
public void FindMatch()
public void Leave()
```

### LobbyController
**Location:** `Assets/_TheLastStand/Scripts/Networking/LobbyController.cs`

Controls game session flow and player readiness.

**Key Features:**
- Ready state management
- Game start coordination
- Scene transition handling
- Solo/multiplayer mode support

## 🔄 Network Flow

### 1. **Lobby Creation Flow**
```mermaid
sequenceDiagram
    participant Host
    participant SteamLobby
    participant MyNetworkManager
    participant Steam
    
    Host->>SteamLobby: CreateLobby()
    SteamLobby->>Steam: CreateLobby()
    Steam->>SteamLobby: OnLobbyCreated()
    SteamLobby->>MyNetworkManager: StartHost()
    MyNetworkManager->>Host: Host started
```

### 2. **Client Join Flow**
```mermaid
sequenceDiagram
    participant Client
    participant SteamLobby
    participant MyNetworkManager
    participant Host
    
    Client->>SteamLobby: JoinLobby(lobbyID)
    SteamLobby->>Steam: JoinLobby()
    Steam->>SteamLobby: OnLobbyEntered()
    SteamLobby->>MyNetworkManager: StartClient()
    MyNetworkManager->>Host: Connect to host
    Host->>Client: Connection established
```

### 3. **Player Spawning Flow**
```mermaid
sequenceDiagram
    participant Client
    participant MyNetworkManager
    participant ForestPlayerManager
    participant MyClient
    
    Client->>MyNetworkManager: OnServerAddPlayer()
    MyNetworkManager->>MyNetworkManager: Check current scene
    alt Map_01 Scene
        MyNetworkManager->>ForestPlayerManager: SpawnNetworkedPlayer()
        ForestPlayerManager->>MyClient: Instantiate with intro setup
    else Other Scenes
        MyNetworkManager->>MyClient: Default spawning
    end
```

## 🎮 Player Management

### MyClient Component
**Location:** `Assets/_TheLastStand/Scripts/Player/MyClient.cs`

Network player representation with Steam integration.

**SyncVars:**
- `PlayerInfoData playerInfo` - Steam username and ID
- `bool IsReady` - Ready state for lobby
- `byte[] avatarData` - Steam profile picture

**Key Features:**
- Steam profile integration
- Ready state management
- Scene-specific behavior
- Character skin handling

### LobbyPlayerList
**Location:** `Assets/_TheLastStand/Scripts/Networking/LobbyPlayerList.cs`

Manages the list of connected players across the network.

**Key Features:**
- Centralized player tracking
- Network synchronization
- Player join/leave handling

## 🔐 Steam Integration

### Authentication Flow
```mermaid
sequenceDiagram
    participant Client
    participant Steam
    participant MyNetworkManager
    participant Server
    
    Client->>Steam: GetSteamID()
    Steam->>Client: Return SteamID
    Client->>MyNetworkManager: Connect with SteamID
    MyNetworkManager->>Server: Validate SteamID
    Server->>MyNetworkManager: Authentication result
```

### Lobby Data Structure
```csharp
// Lobby metadata stored in Steam
"host_address" -> Steam ID of host
"name" -> "PlayerName's Lobby"
"displayable" -> "true"
"location" -> Ping location data
```

## 🚀 Scene Transitions

### MainMenu → Map_01 Flow
```mermaid
sequenceDiagram
    participant LobbyController
    participant MyNetworkManager
    participant AllClients
    participant ForestGameManager
    
    LobbyController->>LobbyController: AllPlayersReady()
    LobbyController->>MyNetworkManager: ServerChangeScene("Map_01")
    MyNetworkManager->>AllClients: Load Map_01
    AllClients->>ForestGameManager: Scene loaded
    ForestGameManager->>ForestGameManager: Start intro sequence
```

## ⚡ Performance Optimization

### Network Performance Monitoring
**Location:** `Assets/_TheLastStand/Scripts/Tools/NetworkPerformanceMonitor.cs`

Tracks network performance metrics:
- SyncVar update frequency
- RPC call frequency
- Network latency (RTT)
- Connection quality

### Best Practices
1. **Minimize SyncVar Updates**
   - Use hooks for change detection
   - Batch related changes
   - Avoid frequent updates

2. **Optimize RPC Usage**
   - Use [ClientRpc] for client-only updates
   - Use [Command] for client-to-server requests
   - Batch multiple operations

3. **Scene Management**
   - Preload critical assets
   - Use additive scene loading where appropriate
   - Clean up resources on scene transitions

## 🔧 Configuration

### Network Settings
```csharp
// In MyNetworkManager
maxConnections = 4;
playerPrefab = MyClientPrefab;
autoCreatePlayer = true;
```

### Steam Settings
```csharp
// In SteamLobby
ELobbyType.k_ELobbyTypePublic
maxConnections = 4
DistanceFilter = Worldwide
```

## 🐛 Common Issues

### Connection Problems
- **Steam not initialized** - Check SteamManager
- **Firewall blocking** - Configure port forwarding
- **Version mismatch** - Ensure all clients use same build

### Spawning Issues
- **Duplicate players** - Check ForestPlayerManager coordination
- **Missing components** - Verify prefab configuration
- **Scene timing** - Ensure proper initialization order

---

**Next:** [Player Management](./PlayerManagement.md) | [Scene Management](./SceneManagement.md)
