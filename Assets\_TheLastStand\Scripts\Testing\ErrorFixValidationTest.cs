using UnityEngine;
using TMPro;
using Mirror;
using System.Collections;

/// <summary>
/// Test script to validate that all the error fixes are working correctly
/// </summary>
public class ErrorFixValidationTest : MonoBehaviour
{
    [Header("Test Settings")]
    [SerializeField] private bool runTestsOnStart = true;
    [SerializeField] private bool verboseLogging = true;
    
    void Start()
    {
        if (runTestsOnStart)
        {
            StartCoroutine(RunAllTests());
        }
    }
    
    IEnumerator RunAllTests()
    {
        Debug.Log("=== ERROR FIX VALIDATION TESTS STARTING ===");
        
        yield return new WaitForSeconds(1f); // Wait for initialization
        
        TestAudioSettingsDontDestroyOnLoad();
        yield return new WaitForSeconds(0.5f);
        
        TestTextMeshProFallbackHandler();
        yield return new WaitForSeconds(0.5f);
        
        TestNetworkManagerValidation();
        yield return new WaitForSeconds(0.5f);
        
        TestForestIntroPlayerUIManager();
        yield return new WaitForSeconds(0.5f);
        
        TestNavMeshValidation();
        
        Debug.Log("=== ERROR FIX VALIDATION TESTS COMPLETED ===");
    }
    
    void TestAudioSettingsDontDestroyOnLoad()
    {
        Debug.Log("Testing AudioSettings DontDestroyOnLoad fix...");
        
        AudioSettings audioSettings = FindObjectOfType<AudioSettings>();
        if (audioSettings != null)
        {
            if (audioSettings.transform.parent == null)
            {
                Debug.Log("✓ AudioSettings is a root GameObject - DontDestroyOnLoad should work correctly.");
            }
            else
            {
                Debug.LogError("✗ AudioSettings is not a root GameObject - this could cause DontDestroyOnLoad issues.");
            }
            
            if (AudioSettings.Instance != null)
            {
                Debug.Log("✓ AudioSettings singleton instance is properly initialized.");
            }
            else
            {
                Debug.LogError("✗ AudioSettings singleton instance is null.");
            }
        }
        else
        {
            Debug.LogWarning("⚠ AudioSettings component not found in scene - this is expected in some scenes.");
        }
    }
    
    void TestTextMeshProFallbackHandler()
    {
        Debug.Log("Testing TextMeshPro fallback handler...");
        
        // Test the static utility method
        string testString = "Test \uC637 Korean character";
        string processedString = TextMeshProFallbackHandler.ProcessStringForFallbackStatic(testString);
        
        if (processedString != testString)
        {
            Debug.Log($"✓ TextMeshPro fallback working: '{testString}' -> '{processedString}'");
        }
        else
        {
            Debug.Log("✓ TextMeshPro fallback handler loaded (no problematic characters in test string).");
        }
        
        // Check for existing fallback handlers in scene
        TextMeshProFallbackHandler[] handlers = FindObjectsOfType<TextMeshProFallbackHandler>();
        Debug.Log($"Found {handlers.Length} TextMeshPro fallback handlers in scene.");
        
        // Check for TextMeshPro components that might need handlers
        TextMeshProUGUI[] textComponents = FindObjectsOfType<TextMeshProUGUI>();
        int componentsWithHandlers = 0;
        
        foreach (TextMeshProUGUI textComponent in textComponents)
        {
            if (textComponent.GetComponent<TextMeshProFallbackHandler>() != null)
            {
                componentsWithHandlers++;
            }
        }
        
        Debug.Log($"Found {textComponents.Length} TextMeshPro components, {componentsWithHandlers} have fallback handlers.");
    }
    
    void TestNetworkManagerValidation()
    {
        Debug.Log("Testing NetworkManager validation fixes...");
        
        MyNetworkManager networkManager = FindObjectOfType<MyNetworkManager>();
        if (networkManager != null)
        {
            Debug.Log("✓ MyNetworkManager found in scene.");
            
            if (networkManager.playerPrefab != null)
            {
                Debug.Log("✓ Player prefab is assigned to NetworkManager.");
                
                // Check if player prefab has MyClient component
                MyClient clientComponent = networkManager.playerPrefab.GetComponent<MyClient>();
                if (clientComponent != null)
                {
                    Debug.Log("✓ Player prefab has MyClient component.");
                }
                else
                {
                    Debug.LogError("✗ Player prefab is missing MyClient component.");
                }
            }
            else
            {
                Debug.LogError("✗ Player prefab is not assigned to NetworkManager.");
            }
            
            // Check LobbyPlayerList
            LobbyPlayerList lobbyPlayerList = FindObjectOfType<LobbyPlayerList>();
            if (lobbyPlayerList != null)
            {
                Debug.Log("✓ LobbyPlayerList found in scene.");
            }
            else
            {
                Debug.LogWarning("⚠ LobbyPlayerList not found - this might cause issues during multiplayer.");
            }
        }
        else
        {
            Debug.LogError("✗ MyNetworkManager not found in scene.");
        }
    }
    
    void TestForestIntroPlayerUIManager()
    {
        Debug.Log("Testing ForestIntroPlayer UI Manager handling...");
        
        ForestIntroPlayer forestPlayer = FindObjectOfType<ForestIntroPlayer>();
        if (forestPlayer != null)
        {
            Debug.Log("✓ ForestIntroPlayer found in scene.");
        }
        else
        {
            Debug.Log("⚠ ForestIntroPlayer not found - this is expected in MainMenu scene.");
        }
        
        ForestMap_UIManager uiManager = FindObjectOfType<ForestMap_UIManager>();
        if (uiManager != null)
        {
            Debug.Log("✓ ForestMap_UIManager found in scene.");
        }
        else
        {
            Debug.Log("⚠ ForestMap_UIManager not found - this is expected in MainMenu scene.");
        }
    }
    
    void TestNavMeshValidation()
    {
        Debug.Log("Testing NavMesh validation...");
        
        // Check if there's NavMesh data in the scene
        UnityEngine.AI.NavMeshTriangulation navMeshData = UnityEngine.AI.NavMesh.CalculateTriangulation();
        if (navMeshData.vertices.Length > 0)
        {
            Debug.Log($"✓ NavMesh found with {navMeshData.vertices.Length} vertices.");
            
            // Test spawn points for NavMesh compatibility
            Transform[] spawnPoints = GameObject.FindObjectsOfType<Transform>();
            int validSpawnPoints = 0;
            
            foreach (Transform spawnPoint in spawnPoints)
            {
                if (spawnPoint.name.Contains("SpawnPoint"))
                {
                    UnityEngine.AI.NavMeshHit hit;
                    if (UnityEngine.AI.NavMesh.SamplePosition(spawnPoint.position, out hit, 5.0f, UnityEngine.AI.NavMesh.AllAreas))
                    {
                        validSpawnPoints++;
                        if (verboseLogging)
                        {
                            Debug.Log($"✓ Spawn point '{spawnPoint.name}' is NavMesh compatible.");
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"⚠ Spawn point '{spawnPoint.name}' at {spawnPoint.position} is not on NavMesh.");
                    }
                }
            }
            
            Debug.Log($"Found {validSpawnPoints} NavMesh-compatible spawn points.");
        }
        else
        {
            Debug.LogWarning("⚠ No NavMesh data found in scene - this might cause player spawning issues.");
        }
    }
    
    [ContextMenu("Run Tests")]
    public void RunTestsManually()
    {
        StartCoroutine(RunAllTests());
    }
}
