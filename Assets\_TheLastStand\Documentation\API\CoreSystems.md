# Core Systems API Reference

## 🏗️ Overview

This document provides detailed API reference for the core systems in TheLastStand project.

## 🌐 MyNetworkManager

**Namespace:** Global  
**Location:** `Assets/_TheLastStand/Scripts/Core/MyNetworkManager.cs`  
**Inherits:** `Mirror.NetworkManager`

Central network management component that coordinates multiplayer functionality and Steam integration.

### Static Properties

```csharp
public static bool isMultiplayer
```
Indicates whether the game is running in multiplayer mode.

```csharp
public static MyNetworkManager instance
```
Singleton instance of the network manager.

### Public Methods

#### OnServerAddPlayer
```csharp
public override void OnServerAddPlayer(NetworkConnectionToClient conn)
```
**Description:** Handles player spawning with scene-aware delegation.  
**Parameters:**
- `conn` - Network connection for the joining player

**Behavior:**
- Checks current scene name
- Delegates to ForestPlayerManager for Map_01 scene
- Uses default spawning for other scenes

#### InitializeSpawnedPlayer
```csharp
public void InitializeSpawnedPlayer(NetworkConnectionToClient conn, GameObject player)
```
**Description:** Initializes a spawned player with Steam data and lobby registration.  
**Parameters:**
- `conn` - Network connection
- `player` - Spawned player GameObject

#### SetMultiplayer
```csharp
public void SetMultiplayer(bool value)
```
**Description:** Configures multiplayer/singleplayer mode.  
**Parameters:**
- `value` - True for multiplayer, false for singleplayer

### Private Methods

#### HandleForestMapSpawning
```csharp
private void HandleForestMapSpawning(NetworkConnectionToClient conn)
```
Coordinates with ForestPlayerManager for specialized forest scene spawning.

#### HandleDefaultSpawning
```csharp
private void HandleDefaultSpawning(NetworkConnectionToClient conn)
```
Performs standard Mirror networking player spawning with NavMesh validation.

---

## 🎮 GameManager

**Namespace:** Global  
**Location:** `Assets/_TheLastStand/Scripts/ParentClasses/GameManager.cs`  
**Inherits:** `MonoBehaviour`

Base class for scene-specific game managers using singleton pattern.

### Static Properties

```csharp
public static GameManager Instance
```
Singleton instance of the game manager.

### Public Methods

#### QuitGame
```csharp
public void QuitGame()
```
**Description:** Exits the application.  
**Usage:** Called from UI quit buttons.

---

## 🌲 ForestGameManager

**Namespace:** Global  
**Location:** `Assets/_TheLastStand/Scripts/Core/ForestGameManager.cs`  
**Inherits:** `GameManager`

Manages forest scene initialization and intro sequence coordination.

### Serialized Fields

```csharp
[SerializeField] private ForestIntroHelicopter forestIntroHelicopter
[SerializeField] private ForestMap_AudioManager forestMapAudioManager
[SerializeField] private ForestPlayerManager forestPlayerManager
```

### Public Methods

#### Start
```csharp
public void Start()
```
**Description:** Initializes forest scene systems and starts intro sequence.

**Initialization Order:**
1. Ensure ForestPlayerManager reference
2. Start helicopter intro sequence (1s delay)
3. Initialize audio systems
4. Log networking state

#### OnHelicopterLanded
```csharp
public void OnHelicopterLanded()
```
**Description:** Handles helicopter landing event.  
**Behavior:** Fades out helicopter audio over 2 seconds.

---

## 🎵 PopupManager

**Namespace:** Global  
**Location:** `Assets/_TheLastStand/Scripts/Core/PopupManager.cs`  
**Inherits:** `MonoBehaviour`

Manages modal dialogs and notification popups throughout the application.

### Static Properties

```csharp
public static PopupManager instance
```
Singleton instance of the popup manager.

### Public Methods

#### Popup_Show
```csharp
public void Popup_Show(string message)
```
**Description:** Displays a popup with the specified message.  
**Parameters:**
- `message` - Text to display in the popup

**Usage Examples:**
```csharp
PopupManager.instance.Popup_Show("Creating Party");
PopupManager.instance.Popup_Show("Finding Match...");
PopupManager.instance.Popup_Show("Party is Locked or Full");
```

#### Popup_Close
```csharp
public void Popup_Close()
```
**Description:** Closes the currently displayed popup.

---

## 🎛️ MainMenu

**Namespace:** Global  
**Location:** `Assets/_TheLastStand/Scripts/Core/MainMenu.cs`  
**Inherits:** `MonoBehaviour`

Manages main menu UI state and player interactions.

### Enums

```csharp
public enum MenuState
{
    Home,
    InParty,
    Settings
}
```

### Static Properties

```csharp
public static MainMenu instance
```
Singleton instance of the main menu.

### Public Properties

```csharp
public MenuState state
```
Current menu state.

### Public Methods

#### SetMenuState
```csharp
public void SetMenuState(MenuState newState)
```
**Description:** Changes the menu state and updates UI accordingly.  
**Parameters:**
- `newState` - Target menu state

**State Behaviors:**
- `Home` - Shows main menu, hides party UI
- `InParty` - Shows party UI, enables ready system
- `Settings` - Shows settings panel

#### CreateParty
```csharp
public void CreateParty()
```
**Description:** Initiates Steam lobby creation and starts hosting.

#### StartSinglePlayer
```csharp
public void StartSinglePlayer()
```
**Description:** Starts a single-player game session.

#### LeaveParty
```csharp
public void LeaveParty()
```
**Description:** Leaves current party and returns to main menu.

#### FindMatch
```csharp
public void FindMatch()
```
**Description:** Initiates matchmaking to find available lobbies.

#### StartGame
```csharp
public void StartGame()
```
**Description:** Starts the game if all players are ready (host only).

#### ToggleReady
```csharp
public void ToggleReady()
```
**Description:** Toggles local player's ready state.

#### UpdateReadyButton
```csharp
public void UpdateReadyButton(bool isReady)
```
**Description:** Updates ready button visual state.  
**Parameters:**
- `isReady` - Current ready state

### Private Methods

#### UpdateLobbyUI
```csharp
private void UpdateLobbyUI()
```
Updates lobby UI with current player information and ready states.

#### ShowCanvasGroup / HideCanvasGroup
```csharp
private void ShowCanvasGroup(CanvasGroup canvasGroup)
private void HideCanvasGroup(CanvasGroup canvasGroup)
```
Utility methods for showing/hiding UI panels.

---

## 🔧 Usage Examples

### Starting a Multiplayer Session
```csharp
// Host creates a party
MainMenu.instance.CreateParty();

// Players join and toggle ready
MainMenu.instance.ToggleReady();

// Host starts the game when all ready
MainMenu.instance.StartGame();
```

### Scene-Specific Initialization
```csharp
// In a custom scene manager
public class CustomSceneManager : GameManager
{
    public override void Start()
    {
        base.Start();
        // Custom initialization
    }
}
```

### Network Player Spawning
```csharp
// Custom spawning logic
public void CustomSpawnPlayer(NetworkConnectionToClient conn)
{
    if (MyNetworkManager.instance != null)
    {
        MyNetworkManager.instance.InitializeSpawnedPlayer(conn, playerObject);
    }
}
```

---

**Next:** [Player Systems API](./PlayerSystems.md) | [Networking API](./Networking.md)
