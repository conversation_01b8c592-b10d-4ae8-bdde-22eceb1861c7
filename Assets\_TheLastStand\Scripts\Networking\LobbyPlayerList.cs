using Mirror;
using System.Collections.Generic;
using UnityEngine;

public class LobbyPlayerList : NetworkBehaviour
{
    public static LobbyPlayerList instance;

    public readonly SyncList<MyClient> allClients = new SyncList<MyClient>();

    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (instance != this)
        {
            Debug.LogWarning("LobbyPlayerList: Multiple instances detected. Destroying duplicate.");
            Destroy(gameObject);
            return;
        }
    }

    public override void OnStopServer()
    {
        base.OnStopServer();
        allClients.Clear(); // Ensure list is cleared if server stops abruptly
    }

    public override void OnStopClient()
    {
        base.OnStopClient();
        if (!isServer) // Only clear on client if not also server
        {
            allClients.Clear();
        }
    }

    private void OnDestroy()
    {
        if (instance == this)
        {
            instance = null;
        }
    }

} 