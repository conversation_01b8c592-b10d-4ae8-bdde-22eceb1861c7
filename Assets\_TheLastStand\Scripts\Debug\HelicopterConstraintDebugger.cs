using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Debug utility for testing and monitoring the helicopter constraint system
/// </summary>
public class HelicopterConstraintDebugger : MonoBehaviour
{
    [Header("Debug Controls")]
    [SerializeField] private bool enableContinuousLogging = false;
    [SerializeField] private float loggingInterval = 2f;
    [SerializeField] private KeyCode activateConstraintsKey = KeyCode.F1;
    [SerializeField] private KeyCode deactivateConstraintsKey = KeyCode.F2;
    [SerializeField] private KeyCode logStatusKey = KeyCode.F3;
    
    [Header("References")]
    [SerializeField] private HelicopterConstraintManager constraintManager;
    
    private float lastLogTime = 0f;
    private List<HelicopterTransformConstraint> foundConstraints = new List<HelicopterTransformConstraint>();

    void Start()
    {
        // Auto-find constraint manager if not assigned
        if (constraintManager == null)
        {
            constraintManager = FindObjectOfType<HelicopterConstraintManager>();
        }
        
        Debug.Log("HelicopterConstraintDebugger: Initialized. Use F1/F2 to activate/deactivate constraints, F3 for status.");
    }

    void Update()
    {
        HandleDebugInput();
        
        if (enableContinuousLogging && Time.time - lastLogTime >= loggingInterval)
        {
            LogConstraintStatus();
            lastLogTime = Time.time;
        }
    }

    private void HandleDebugInput()
    {
        if (Input.GetKeyDown(activateConstraintsKey))
        {
            ActivateAllConstraints();
        }
        
        if (Input.GetKeyDown(deactivateConstraintsKey))
        {
            DeactivateAllConstraints();
        }
        
        if (Input.GetKeyDown(logStatusKey))
        {
            LogConstraintStatus();
        }
    }

    private void ActivateAllConstraints()
    {
        if (constraintManager != null)
        {
            constraintManager.ForceActivateConstraints();
            Debug.Log("HelicopterConstraintDebugger: Manually activated all constraints");
        }
        else
        {
            Debug.LogWarning("HelicopterConstraintDebugger: No constraint manager found");
        }
    }

    private void DeactivateAllConstraints()
    {
        if (constraintManager != null)
        {
            constraintManager.ForceDeactivateConstraints();
            Debug.Log("HelicopterConstraintDebugger: Manually deactivated all constraints");
        }
        else
        {
            Debug.LogWarning("HelicopterConstraintDebugger: No constraint manager found");
        }
    }

    private void LogConstraintStatus()
    {
        Debug.Log("=== HELICOPTER CONSTRAINT STATUS ===");
        
        // Log constraint manager status
        if (constraintManager != null)
        {
            Debug.Log($"Constraint Manager: {constraintManager.GetConstraintStatus()}");
        }
        else
        {
            Debug.Log("Constraint Manager: NOT FOUND");
        }
        
        // Find and log individual constraint status
        foundConstraints.Clear();
        foundConstraints.AddRange(FindObjectsOfType<HelicopterTransformConstraint>());
        
        Debug.Log($"Found {foundConstraints.Count} constraint components:");
        
        foreach (var constraint in foundConstraints)
        {
            if (constraint != null)
            {
                LogIndividualConstraintStatus(constraint);
            }
        }
        
        Debug.Log("=== END CONSTRAINT STATUS ===");
    }

    private void LogIndividualConstraintStatus(HelicopterTransformConstraint constraint)
    {
        GameObject player = constraint.gameObject;
        Transform spawnParent = player.transform.parent;
        
        string parentInfo = spawnParent != null ? $"Parent: {spawnParent.name}" : "Parent: NONE";
        string constraintStatus = constraint.ConstraintsActive ? "ACTIVE" : "INACTIVE";
        string validSpawnPoint = constraint.HasValidSpawnPoint ? "VALID" : "INVALID";
        
        Debug.Log($"  Player: {player.name} | Constraints: {constraintStatus} | Spawn Point: {validSpawnPoint} | {parentInfo}");
        Debug.Log($"    Local Pos: {player.transform.localPosition:F3} | Local Rot: {player.transform.localRotation.eulerAngles:F1}");
        Debug.Log($"    World Pos: {player.transform.position:F3} | World Rot: {player.transform.rotation.eulerAngles:F1}");
        
        if (spawnParent != null)
        {
            Debug.Log($"    Spawn Point World Pos: {spawnParent.position:F3} | World Rot: {spawnParent.rotation.eulerAngles:F1}");
        }
    }

    void OnGUI()
    {
        if (!enableContinuousLogging) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 400, 200));
        GUILayout.Label("Helicopter Constraint Debugger", GUI.skin.box);
        
        if (constraintManager != null)
        {
            GUILayout.Label($"Manager Status: {constraintManager.GetConstraintStatus()}");
        }
        else
        {
            GUILayout.Label("Manager Status: NOT FOUND", GUI.skin.box);
        }
        
        GUILayout.Label($"Constraint Components Found: {foundConstraints.Count}");
        
        if (GUILayout.Button("Activate All Constraints"))
        {
            ActivateAllConstraints();
        }
        
        if (GUILayout.Button("Deactivate All Constraints"))
        {
            DeactivateAllConstraints();
        }
        
        if (GUILayout.Button("Log Status"))
        {
            LogConstraintStatus();
        }
        
        GUILayout.EndArea();
    }
}
