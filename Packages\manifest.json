{"dependencies": {"com.rlabrecque.steamworks.net": "https://github.com/rlabrecque/Steamworks.NET.git?path=/com.rlabrecque.steamworks.net", "com.unity.ai.navigation": "2.0.7", "com.unity.collab-proxy": "2.8.2", "com.unity.feature.development": "1.0.2", "com.unity.multiplayer.center": "1.0.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.postprocessing": "3.4.0", "com.unity.render-pipelines.high-definition": "17.0.4", "com.unity.timeline": "1.8.7", "com.unity.ugui": "2.0.0", "com.unity.visualscripting": "1.9.6", "com.unity.modules.accessibility": "1.0.0", "com.unity.modules.ai": "1.0.0", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.cloth": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.screencapture": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.umbra": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.unitywebrequesttexture": "1.0.0", "com.unity.modules.unitywebrequestwww": "1.0.0", "com.unity.modules.vehicles": "1.0.0", "com.unity.modules.video": "1.0.0", "com.unity.modules.vr": "1.0.0", "com.unity.modules.wind": "1.0.0", "com.unity.modules.xr": "1.0.0", "com.ivanmurzak.unity.mcp": "0.8.1"}, "scopedRegistries": [{"name": "package.openupm.com", "url": "https://package.openupm.com", "scopes": ["com.ivanmurzak.unity.mcp", "org.nuget.microsoft.aspnetcore.connections.abstractions", "org.nuget.microsoft.aspnetcore.http.connections.client", "org.nuget.microsoft.aspnetcore.http.connections.common", "org.nuget.microsoft.aspnetcore.signalr.client", "org.nuget.microsoft.aspnetcore.signalr.client.core", "org.nuget.microsoft.aspnetcore.signalr.common", "org.nuget.microsoft.aspnetcore.signalr.protocols.json", "org.nuget.microsoft.bcl.asyncinterfaces", "org.nuget.microsoft.bcl.memory", "org.nuget.microsoft.bcl.timeprovider", "org.nuget.microsoft.codeanalysis.analyzers", "org.nuget.microsoft.codeanalysis.common", "org.nuget.microsoft.codeanalysis.csharp", "org.nuget.microsoft.extensions.caching.abstractions", "org.nuget.microsoft.extensions.configuration", "org.nuget.microsoft.extensions.configuration.abstractions", "org.nuget.microsoft.extensions.configuration.binder", "org.nuget.microsoft.extensions.configuration.commandline", "org.nuget.microsoft.extensions.configuration.environmentvariables", "org.nuget.microsoft.extensions.configuration.fileextensions", "org.nuget.microsoft.extensions.configuration.json", "org.nuget.microsoft.extensions.configuration.usersecrets", "org.nuget.microsoft.extensions.dependencyinjection", "org.nuget.microsoft.extensions.dependencyinjection.abstractions", "org.nuget.microsoft.extensions.diagnostics", "org.nuget.microsoft.extensions.diagnostics.abstractions", "org.nuget.microsoft.extensions.features", "org.nuget.microsoft.extensions.fileproviders.abstractions", "org.nuget.microsoft.extensions.fileproviders.physical", "org.nuget.microsoft.extensions.filesystemglobbing", "org.nuget.microsoft.extensions.hosting", "org.nuget.microsoft.extensions.hosting.abstractions", "org.nuget.microsoft.extensions.logging", "org.nuget.microsoft.extensions.logging.abstractions", "org.nuget.microsoft.extensions.logging.configuration", "org.nuget.microsoft.extensions.logging.console", "org.nuget.microsoft.extensions.logging.debug", "org.nuget.microsoft.extensions.logging.eventlog", "org.nuget.microsoft.extensions.logging.eventsource", "org.nuget.microsoft.extensions.options", "org.nuget.microsoft.extensions.options.configurationextensions", "org.nuget.microsoft.extensions.primitives", "org.nuget.r3", "org.nuget.system.buffers", "org.nuget.system.collections.immutable", "org.nuget.system.componentmodel.annotations", "org.nuget.system.diagnostics.diagnosticsource", "org.nuget.system.diagnostics.eventlog", "org.nuget.system.io.pipelines", "org.nuget.system.memory", "org.nuget.system.net.serversentevents", "org.nuget.system.numerics.vectors", "org.nuget.system.reflection.metadata", "org.nuget.system.runtime.compilerservices.unsafe", "org.nuget.system.security.principal.windows", "org.nuget.system.text.encoding.codepages", "org.nuget.system.text.encodings.web", "org.nuget.system.text.json", "org.nuget.system.threading.channels", "org.nuget.system.threading.tasks.extensions"]}]}