using Mirror;
using UnityEngine;

public class PlayerIdentity : NetworkBehaviour
{
    [SyncVar]
    public int PlayerSlotId = 0; // Assign this when the player is setup in the lobby/game

    // Example: A server-only method to assign slot ID if not done elsewhere
    [Server]
    public void Server_SetSlotId(int slotId)
    {
        PlayerSlotId = slotId;
        Debug.Log($"Player {netId} assigned Slot ID: {slotId}");
    }
} 