using UnityEngine;
using System.Collections.Generic;
using Mirror;

/// <summary>
/// Manages helicopter transform constraints for all players based on helicopter flight state.
/// Coordinates constraint activation/deactivation during different phases of helicopter sequence.
/// </summary>
public class HelicopterConstraintManager : NetworkBehaviour
{
    public static HelicopterConstraintManager Instance { get; private set; }

    [Header("Constraint Configuration")]
    [SerializeField] private bool enableConstraintsDuringFlight = true;
    [SerializeField] private bool disableConstraintsOnLanding = true;
    [SerializeField] private bool disableConstraintsOnExit = true;
    
    [Header("Helicopter References")]
    [SerializeField] private ForestIntroHelicopter helicopter;
    [SerializeField] private HelicopterExitManager exitManager;
    
    [Header("Timing")]
    [SerializeField] private float constraintActivationDelay = 1f;
    [SerializeField] private float constraintDeactivationDelay = 0.5f;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;

    // State tracking
    private List<HelicopterTransformConstraint> registeredConstraints = new List<HelicopterTransformConstraint>();
    private bool constraintsCurrentlyActive = false;
    private Helicopter.HelicopterState lastHelicopterState = Helicopter.HelicopterState.None;

    // Network synchronization
    [SyncVar(hook = nameof(OnGlobalConstraintsChanged))]
    private bool networkGlobalConstraintsActive = false;

    public bool GlobalConstraintsActive 
    { 
        get => constraintsCurrentlyActive; 
        private set 
        { 
            if (constraintsCurrentlyActive != value)
            {
                constraintsCurrentlyActive = value;
                if (isServer)
                {
                    networkGlobalConstraintsActive = value;
                }
                OnGlobalConstraintsChanged(constraintsCurrentlyActive, value);
            }
        } 
    }

    public int RegisteredPlayerCount => registeredConstraints.Count;

    void Awake()
    {
        // Singleton pattern
        if (Instance == null)
        {
            Instance = this;
        }
        else if (Instance != this)
        {
            Debug.LogWarning("Multiple HelicopterConstraintManager instances detected. Destroying duplicate.");
            Destroy(gameObject);
            return;
        }
    }

    void Start()
    {
        InitializeConstraintManager();
    }

    private void InitializeConstraintManager()
    {
        // Auto-find helicopter if not assigned
        if (helicopter == null)
        {
            helicopter = FindObjectOfType<ForestIntroHelicopter>();
            if (helicopter != null)
            {
                Debug.Log("HelicopterConstraintManager: Auto-found ForestIntroHelicopter");
            }
        }

        // Auto-find exit manager if not assigned
        if (exitManager == null)
        {
            exitManager = FindObjectOfType<HelicopterExitManager>();
            if (exitManager != null)
            {
                Debug.Log("HelicopterConstraintManager: Auto-found HelicopterExitManager");
            }
        }

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterConstraintManager: Initialized with helicopter: {(helicopter != null ? helicopter.name : "None")}, " +
                     $"exit manager: {(exitManager != null ? exitManager.name : "None")}");
        }
    }

    void Update()
    {
        if (!isServer) return;

        MonitorHelicopterState();
    }

    private void MonitorHelicopterState()
    {
        if (helicopter == null) return;

        Helicopter.HelicopterState currentState = helicopter.CurrentState;

        // Check for state changes
        if (currentState != lastHelicopterState)
        {
            HandleHelicopterStateChange(lastHelicopterState, currentState);
            lastHelicopterState = currentState;
        }
    }

    [Server]
    private void HandleHelicopterStateChange(Helicopter.HelicopterState oldState, Helicopter.HelicopterState newState)
    {
        if (!isServer) return;

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterConstraintManager: Helicopter state changed from {oldState} to {newState}");
        }

        switch (newState)
        {
            case Helicopter.HelicopterState.InitializingPath:
            case Helicopter.HelicopterState.MovingToWaypoint:
            case Helicopter.HelicopterState.Rotating:
                if (enableConstraintsDuringFlight && !GlobalConstraintsActive)
                {
                    StartCoroutine(ActivateConstraintsWithDelay());
                }
                break;

            case Helicopter.HelicopterState.Landing:
                if (disableConstraintsOnLanding && GlobalConstraintsActive)
                {
                    StartCoroutine(DeactivateConstraintsWithDelay());
                }
                break;

            case Helicopter.HelicopterState.PathComplete:
                if (GlobalConstraintsActive)
                {
                    StartCoroutine(DeactivateConstraintsWithDelay());
                }
                break;

            case Helicopter.HelicopterState.None: // Idle
                // Keep current constraint state during none
                break;
        }
    }

    private System.Collections.IEnumerator ActivateConstraintsWithDelay()
    {
        yield return new WaitForSeconds(constraintActivationDelay);
        ActivateAllConstraints();
    }

    private System.Collections.IEnumerator DeactivateConstraintsWithDelay()
    {
        yield return new WaitForSeconds(constraintDeactivationDelay);
        DeactivateAllConstraints();
    }

    /// <summary>
    /// Registers a player's constraint component with the manager
    /// </summary>
    public void RegisterConstrainedPlayer(HelicopterTransformConstraint constraint)
    {
        if (constraint == null)
        {
            Debug.LogWarning("HelicopterConstraintManager: Attempted to register null constraint");
            return;
        }

        if (!registeredConstraints.Contains(constraint))
        {
            registeredConstraints.Add(constraint);

            // Apply current global constraint state to newly registered player
            // Add safety check for server state and constraint validity
            try
            {
                if (isServer && GlobalConstraintsActive && constraint.HasValidSpawnPoint)
                {
                    constraint.ActivateConstraints();
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"HelicopterConstraintManager: Failed to activate constraints for newly registered player {constraint.gameObject.name}: {ex.Message}");
            }

            if (showDebugLogs)
            {
                Debug.Log($"HelicopterConstraintManager: Registered constraint for {constraint.gameObject.name}. Total: {registeredConstraints.Count}");
            }
        }
    }

    /// <summary>
    /// Unregisters a player's constraint component from the manager
    /// </summary>
    public void UnregisterConstrainedPlayer(HelicopterTransformConstraint constraint)
    {
        if (constraint == null) return;

        if (registeredConstraints.Remove(constraint))
        {
            if (showDebugLogs)
            {
                Debug.Log($"HelicopterConstraintManager: Unregistered constraint for {constraint.gameObject.name}. Total: {registeredConstraints.Count}");
            }
        }
    }

    /// <summary>
    /// Manually activates constraints for all registered players
    /// </summary>
    [Server]
    public void ActivateAllConstraints()
    {
        if (!isServer) return;

        GlobalConstraintsActive = true;

        foreach (var constraint in registeredConstraints)
        {
            if (constraint != null && constraint.HasValidSpawnPoint)
            {
                constraint.ActivateConstraints();
            }
        }

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterConstraintManager: Activated constraints for {registeredConstraints.Count} players");
        }
    }

    /// <summary>
    /// Manually deactivates constraints for all registered players
    /// </summary>
    [Server]
    public void DeactivateAllConstraints()
    {
        if (!isServer) return;

        GlobalConstraintsActive = false;

        foreach (var constraint in registeredConstraints)
        {
            if (constraint != null)
            {
                constraint.DeactivateConstraints();
            }
        }

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterConstraintManager: Deactivated constraints for {registeredConstraints.Count} players");
        }
    }

    /// <summary>
    /// Called by HelicopterExitManager when exit sequence begins
    /// </summary>
    [Server]
    public void OnHelicopterExitSequenceStarted()
    {
        if (!isServer) return;

        if (disableConstraintsOnExit && GlobalConstraintsActive)
        {
            if (showDebugLogs)
            {
                Debug.Log("HelicopterConstraintManager: Exit sequence started, deactivating constraints");
            }
            DeactivateAllConstraints();
        }
    }

    private void OnGlobalConstraintsChanged(bool oldValue, bool newValue)
    {
        constraintsCurrentlyActive = newValue;
        
        if (showDebugLogs)
        {
            Debug.Log($"HelicopterConstraintManager: Global constraints {(constraintsCurrentlyActive ? "activated" : "deactivated")}");
        }
    }

    /// <summary>
    /// Gets constraint status for debugging
    /// </summary>
    public string GetConstraintStatus()
    {
        return $"Global Active: {GlobalConstraintsActive}, " +
               $"Registered Players: {registeredConstraints.Count}, " +
               $"Helicopter State: {(helicopter != null ? helicopter.CurrentState.ToString() : "No Helicopter")}";
    }

    void OnDestroy()
    {
        if (Instance == this)
        {
            Instance = null;
        }
    }

    // Public API for external systems
    [Server]
    public void ForceActivateConstraints() => ActivateAllConstraints();
    
    [Server]
    public void ForceDeactivateConstraints() => DeactivateAllConstraints();
}
