using UnityEngine;

public class ForestIntroReplicatedPlayer : <PERSON>o<PERSON><PERSON><PERSON><PERSON>
{
    [Header("References")]
    [<PERSON>lt<PERSON>("The transform of the replicated player's visual model. This is what will be rotated.")]
    [SerializeField] private Transform playerModel;

    private float initialYRotationOffset = 0f;

    void Awake()
    {
        if (playerModel == null)
        {
            // If not assigned, assume the model is this transform or a child named "Model"
            Transform modelChild = transform.Find("Model");
            if (modelChild != null)
            {
                playerModel = modelChild;
            }
            else
            {
                playerModel = transform; // Default to this object's transform
            }
            Debug.LogWarning($"ForestIntroReplicatedPlayer on {gameObject.name}: 'playerModel' was not explicitly assigned. Defaulted to {(playerModel == transform ? "this object\'s transform" : "child \'Model\'.")}", this);
        }
    }

    public void SetInitialYRotationOffset(float offset)
    {
        this.initialYRotationOffset = offset;
    }

    /// <summary>
    /// Updates the replicated player's visual model to face a new world Z rotation,
    /// while keeping its world X and Y rotations unchanged.
    /// </summary>
    /// <param name="newWorldAngle">The target world Z rotation in degrees.</param>
    public void SetVisualWorldZRotation(float newWorldAngle)
    {
        if (playerModel != null)
        {
            playerModel.rotation = Quaternion.Euler(0f, initialYRotationOffset, newWorldAngle);
        }
        else
        {
            Debug.LogError("ForestIntroReplicatedPlayer: 'playerModel' is null. Cannot update rotation.", this);
        }
    }

    /// <summary>
    /// Updates the replicated player's visual model based on a full world orientation,
    /// but only applies the Z component to its rotation, keeping world X and Y unchanged.
    /// </summary>
    /// <param name="newWorldOrientation">The target full world orientation.</param>
    public void SetVisualWorldZRotation(Quaternion newWorldOrientation)
    {
        SetVisualWorldZRotation(newWorldOrientation.eulerAngles.y); // Use YAW from the quaternion for Z rotation visual
    }
}
