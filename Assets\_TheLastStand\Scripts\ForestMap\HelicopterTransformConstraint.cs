using UnityEngine;
using Mirror;

/// <summary>
/// Constrains a networked player's transform during helicopter flight sequences.
/// Maintains position relative to spawn point and limits rotation for social interaction.
/// </summary>
public class HelicopterTransformConstraint : NetworkBehaviour
{
    [Header("Position Constraints")]
    [SerializeField] private bool constrainPosition = true;
    [SerializeField] private Vector3 constrainedLocalPosition = Vector3.zero;
    
    [Header("Rotation Constraints")]
    [SerializeField] private bool constrainRotation = true;
    [SerializeField] private bool lockXRotation = true;
    [SerializeField] private bool lockZRotation = true;
    [SerializeField] private bool allowYRotation = true;
    [SerializeField] private float yRotationMin = -90f;
    [SerializeField] private float yRotationMax = 90f;
    
    [Header("Constraint Behavior")]
    [SerializeField] private float constraintSmoothness = 10f;
    [SerializeField] private bool enableVisualFeedback = true;
    
    [Head<PERSON>("Debug")]
    [SerializeField] private bool showDebugInfo = false;
    [SerializeField] private bool enableDetailedLogging = true;

    // Private state
    private Transform spawnPointParent;
    private Vector3 initialLocalPosition;
    private Quaternion initialLocalRotation;
    private float currentYRotation = 0f;
    private bool constraintsActive = false;
    
    // Component references
    private ForestIntroPlayer forestIntroPlayer;
    private ForestPlayer forestPlayer;
    
    // Network synchronization
    [SyncVar(hook = nameof(OnConstraintsActiveChanged))]
    private bool networkConstraintsActive = false;

    public bool ConstraintsActive 
    { 
        get => constraintsActive; 
        private set 
        { 
            if (constraintsActive != value)
            {
                constraintsActive = value;
                if (isServer)
                {
                    networkConstraintsActive = value;
                }
                OnConstraintsActiveChanged(constraintsActive, value);
            }
        } 
    }

    public bool HasValidSpawnPoint => spawnPointParent != null;

    void Start()
    {
        // Delay initialization to ensure proper setup
        StartCoroutine(DelayedInitialization());
    }

    private System.Collections.IEnumerator DelayedInitialization()
    {
        // Wait a frame to ensure all components are properly initialized
        yield return null;

        // Wait for NetworkBehaviour to be properly initialized
        int maxWaitFrames = 30; // Max 0.5 seconds at 60fps
        int waitFrames = 0;

        // Add null checks to prevent NullReferenceException
        while (waitFrames < maxWaitFrames)
        {
            // Check if NetworkBehaviour is initialized safely
            bool networkInitialized = false;
            try
            {
                // Safe check for network state - these properties might throw if not initialized
                networkInitialized = isOwned || isServer || isClient;
            }
            catch (System.Exception)
            {
                // NetworkBehaviour not yet ready, continue waiting
                networkInitialized = false;
            }

            if (networkInitialized)
            {
                break;
            }

            yield return null;
            waitFrames++;
        }

        if (waitFrames >= maxWaitFrames)
        {
            Debug.LogWarning($"HelicopterTransformConstraint: NetworkBehaviour initialization timeout for {gameObject.name}");
        }

        InitializeConstraintSystem();
    }

    private void InitializeConstraintSystem()
    {
        // Store spawn point parent (should be set during spawning)
        spawnPointParent = transform.parent;

        if (spawnPointParent != null)
        {
            initialLocalPosition = transform.localPosition;
            initialLocalRotation = transform.localRotation;
            currentYRotation = transform.localEulerAngles.y;

            if (enableDetailedLogging)
            {
                Debug.Log($"HelicopterTransformConstraint: Initialized for {gameObject.name} with spawn point {spawnPointParent.name}. " +
                         $"Initial local pos: {initialLocalPosition}, rot: {initialLocalRotation.eulerAngles}");
            }
        }
        else
        {
            Debug.LogWarning($"HelicopterTransformConstraint: No spawn point parent found for {gameObject.name}. Constraints will not function properly.");
        }

        // Get player component references
        forestIntroPlayer = GetComponent<ForestIntroPlayer>();
        forestPlayer = GetComponent<ForestPlayer>();

        // Register with constraint manager
        HelicopterConstraintManager.Instance?.RegisterConstrainedPlayer(this);

        // If constraints should be active immediately, apply initial correction
        if (constraintsActive)
        {
            ApplyImmediateConstraintCorrection();
        }
    }

    /// <summary>
    /// Immediately applies constraint corrections without smooth lerping
    /// </summary>
    private void ApplyImmediateConstraintCorrection()
    {
        if (spawnPointParent == null) return;

        Debug.Log($"HelicopterTransformConstraint: Applying immediate constraint correction for {gameObject.name}");

        // Immediately set position to constrained position
        if (constrainPosition)
        {
            transform.localPosition = constrainedLocalPosition;
        }

        // Immediately set rotation to constrained rotation
        if (constrainRotation)
        {
            Vector3 targetEuler = Vector3.zero;

            // Only allow Y rotation if specified
            if (allowYRotation)
            {
                float currentY = transform.localEulerAngles.y;
                float normalizedY = NormalizeAngle(currentY);
                targetEuler.y = Mathf.Clamp(normalizedY, yRotationMin, yRotationMax);
            }

            transform.localRotation = Quaternion.Euler(targetEuler);
        }

        Debug.Log($"HelicopterTransformConstraint: After correction - Local pos: {transform.localPosition}, rot: {transform.localRotation.eulerAngles}");
    }

    void Update()
    {
        // Add null checking and ensure NetworkBehaviour is properly initialized
        if (!IsValidForUpdate()) return;

        if (constraintsActive)
        {
            EnforceConstraints();
        }

        if (showDebugInfo)
        {
            DisplayDebugInfo();
        }
    }

    private bool IsValidForUpdate()
    {
        // Ensure we have a valid transform first
        if (transform == null)
        {
            Debug.LogError("HelicopterTransformConstraint: Transform is null!");
            return false;
        }

        // Check if NetworkBehaviour is properly initialized with safe access
        bool networkInitialized = false;
        try
        {
            // Safe check for network state - these properties might throw if not initialized
            networkInitialized = isOwned || isServer || isClient;
        }
        catch (System.Exception)
        {
            // NetworkBehaviour not yet ready
            return false;
        }

        if (!networkInitialized)
        {
            return false; // NetworkBehaviour not yet initialized
        }

        // Only process on local player or server
        try
        {
            if (!isLocalPlayer && !isServer)
            {
                return false;
            }
        }
        catch (System.Exception)
        {
            // Network properties not ready
            return false;
        }

        return true;
    }

    private void EnforceConstraints()
    {
        if (spawnPointParent == null) return;

        // Enforce position constraints
        if (constrainPosition)
        {
            Vector3 targetLocalPosition = constrainedLocalPosition;
            if (Vector3.Distance(transform.localPosition, targetLocalPosition) > 0.001f)
            {
                transform.localPosition = Vector3.Lerp(transform.localPosition, targetLocalPosition, 
                    constraintSmoothness * Time.deltaTime);
            }
        }

        // Enforce rotation constraints
        if (constrainRotation)
        {
            Vector3 currentEuler = transform.localEulerAngles;
            Vector3 targetEuler = currentEuler;

            // Lock X rotation (pitch)
            if (lockXRotation)
            {
                targetEuler.x = 0f;
            }

            // Lock Z rotation (roll)
            if (lockZRotation)
            {
                targetEuler.z = 0f;
            }

            // Constrain Y rotation (yaw) within limits
            if (allowYRotation)
            {
                float normalizedY = NormalizeAngle(currentEuler.y);
                targetEuler.y = Mathf.Clamp(normalizedY, yRotationMin, yRotationMax);
            }
            else
            {
                targetEuler.y = 0f;
            }

            // Apply rotation constraints smoothly
            Quaternion targetRotation = Quaternion.Euler(targetEuler);
            if (Quaternion.Angle(transform.localRotation, targetRotation) > 0.1f)
            {
                transform.localRotation = Quaternion.Lerp(transform.localRotation, targetRotation, 
                    constraintSmoothness * Time.deltaTime);
            }
        }
    }

    private float NormalizeAngle(float angle)
    {
        while (angle > 180f) angle -= 360f;
        while (angle < -180f) angle += 360f;
        return angle;
    }

    /// <summary>
    /// Activates helicopter constraints for this player
    /// </summary>
    [Server]
    public void ActivateConstraints()
    {
        // Add comprehensive null checks
        if (transform == null)
        {
            Debug.LogError("HelicopterTransformConstraint: Cannot activate constraints - transform is null!");
            return;
        }

        // Safe check for server state
        try
        {
            if (!isServer) return;
        }
        catch (System.Exception)
        {
            Debug.LogWarning("HelicopterTransformConstraint: Cannot check server state during ActivateConstraints - NetworkBehaviour not ready");
            return;
        }

        Debug.Log($"HelicopterTransformConstraint: Activating constraints for {gameObject.name}. " +
                 $"Current local pos: {transform.localPosition}, rot: {transform.localRotation.eulerAngles}");

        ConstraintsActive = true;

        // Apply immediate correction when constraints are activated
        if (spawnPointParent != null)
        {
            ApplyImmediateConstraintCorrection();
        }
        else
        {
            Debug.LogWarning($"HelicopterTransformConstraint: Cannot apply constraint correction for {gameObject.name} - spawnPointParent is null. " +
                           "This may indicate the constraint was activated before proper initialization.");
        }
    }

    /// <summary>
    /// Deactivates helicopter constraints for this player
    /// </summary>
    [Server]
    public void DeactivateConstraints()
    {
        if (!isServer) return;
        
        ConstraintsActive = false;
        Debug.Log($"HelicopterTransformConstraint: Deactivated constraints for {gameObject.name}");
    }

    /// <summary>
    /// Checks if the player's Y rotation is within allowed limits
    /// </summary>
    public bool IsYRotationWithinLimits(float yRotation)
    {
        if (!allowYRotation) return false;
        
        float normalizedY = NormalizeAngle(yRotation);
        return normalizedY >= yRotationMin && normalizedY <= yRotationMax;
    }

    /// <summary>
    /// Clamps Y rotation to allowed limits
    /// </summary>
    public float ClampYRotation(float yRotation)
    {
        if (!allowYRotation) return 0f;
        
        float normalizedY = NormalizeAngle(yRotation);
        return Mathf.Clamp(normalizedY, yRotationMin, yRotationMax);
    }

    private void OnConstraintsActiveChanged(bool oldValue, bool newValue)
    {
        constraintsActive = newValue;

        Debug.Log($"HelicopterTransformConstraint: Constraints {(constraintsActive ? "activated" : "deactivated")} for {gameObject.name}. " +
                 $"Local pos: {transform.localPosition}, rot: {transform.localRotation.eulerAngles}");

        if (constraintsActive)
        {
            // Store current state when constraints activate
            if (spawnPointParent != null)
            {
                currentYRotation = transform.localEulerAngles.y;

                // Apply immediate correction on activation
                ApplyImmediateConstraintCorrection();
            }
        }
    }

    private void DisplayDebugInfo()
    {
        if (spawnPointParent == null) return;

        Vector3 localPos = transform.localPosition;
        Vector3 localRot = transform.localEulerAngles;
        
        Debug.Log($"Constraint Debug - {gameObject.name}: " +
                 $"Active: {constraintsActive}, " +
                 $"LocalPos: {localPos:F3}, " +
                 $"LocalRot: {localRot:F1}, " +
                 $"YInLimits: {IsYRotationWithinLimits(localRot.y)}");
    }

    void OnDestroy()
    {
        // Unregister from constraint manager
        HelicopterConstraintManager.Instance?.UnregisterConstrainedPlayer(this);
    }

    void OnDrawGizmosSelected()
    {
        if (spawnPointParent == null) return;

        // Draw constraint visualization
        Gizmos.color = constraintsActive ? Color.red : Color.yellow;
        Gizmos.matrix = spawnPointParent.localToWorldMatrix;
        
        // Draw position constraint
        if (constrainPosition)
        {
            Gizmos.DrawWireCube(constrainedLocalPosition, Vector3.one * 0.1f);
        }
        
        // Draw rotation limits
        if (constrainRotation && allowYRotation)
        {
            Gizmos.color = Color.blue;
            Vector3 center = constrainedLocalPosition;
            
            // Draw Y rotation arc
            float arcRadius = 0.5f;
            Vector3 minDir = Quaternion.Euler(0, yRotationMin, 0) * Vector3.forward;
            Vector3 maxDir = Quaternion.Euler(0, yRotationMax, 0) * Vector3.forward;
            
            Gizmos.DrawLine(center, center + minDir * arcRadius);
            Gizmos.DrawLine(center, center + maxDir * arcRadius);
        }
    }
}
