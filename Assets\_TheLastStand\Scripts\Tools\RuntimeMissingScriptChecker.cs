using UnityEngine;
using System.Collections.Generic;

public class RuntimeMissingScriptChecker : MonoBehaviour
{
    [System.Serializable]
    public class MissingScriptInfo
    {
        public string gameObjectName;
        public string gameObjectPath;
        public int missingScriptCount;
    }

    void Start()
    {
        CheckForMissingScripts();
    }

    public void CheckForMissingScripts()
    {
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        int totalMissingScripts = 0;
        List<GameObject> objectsWithMissingScripts = new List<GameObject>();

        foreach (GameObject obj in allObjects)
        {
            Component[] components = obj.GetComponents<Component>();
            int missingCount = 0;

            foreach (Component component in components)
            {
                if (component == null)
                {
                    missingCount++;
                    totalMissingScripts++;
                }
            }

            if (missingCount > 0)
            {
                objectsWithMissingScripts.Add(obj);
                string path = GetGameObjectPath(obj);
                Debug.LogError($"Missing Script Found: GameObject '{obj.name}' at path '{path}' has {missingCount} missing script(s)", obj);
            }
        }

        if (totalMissingScripts == 0)
        {
            Debug.Log("RuntimeMissingScriptChecker: No missing scripts found in the scene.");
        }
        else
        {
            Debug.LogError($"RuntimeMissingScriptChecker: Total missing scripts found: {totalMissingScripts} across {objectsWithMissingScripts.Count} GameObjects");

            // Offer automatic cleanup option in development builds
            #if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (autoCleanupMissingScripts)
            {
                CleanupMissingScripts(objectsWithMissingScripts);
            }
            else
            {
                Debug.LogWarning("RuntimeMissingScriptChecker: Set 'autoCleanupMissingScripts' to true to automatically remove missing script references.");
            }
            #endif
        }
    }

    #if UNITY_EDITOR || DEVELOPMENT_BUILD
    [Header("Cleanup Options")]
    [SerializeField] private bool autoCleanupMissingScripts = false;

    private void CleanupMissingScripts(List<GameObject> objectsWithMissingScripts)
    {
        int cleanedCount = 0;

        foreach (GameObject obj in objectsWithMissingScripts)
        {
            if (obj == null) continue;

            // Get all components including missing ones
            Component[] components = obj.GetComponents<Component>();
            List<Component> validComponents = new List<Component>();

            foreach (Component component in components)
            {
                if (component != null)
                {
                    validComponents.Add(component);
                }
                else
                {
                    cleanedCount++;
                }
            }

            Debug.Log($"RuntimeMissingScriptChecker: Cleaned missing scripts from '{obj.name}'. Removed {components.Length - validComponents.Count} missing references.", obj);
        }

        if (cleanedCount > 0)
        {
            Debug.Log($"RuntimeMissingScriptChecker: Successfully cleaned {cleanedCount} missing script references.");
        }
    }
    #endif
    
    private string GetGameObjectPath(GameObject obj)
    {
        string path = obj.name;
        Transform parent = obj.transform.parent;
        
        while (parent != null)
        {
            path = parent.name + "/" + path;
            parent = parent.parent;
        }
        
        return path;
    }
}