using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

#if UNITY_EDITOR
[System.Serializable]
public class MissingScriptFinder : EditorWindow
{
    [MenuItem("Tools/Missing Script Finder")]
    public static void ShowWindow()
    {
        GetWindow<MissingScriptFinder>("Missing Script Finder");
    }

    private Vector2 scrollPosition;
    private List<GameObject> objectsWithMissingScripts = new List<GameObject>();

    private void OnGUI()
    {
        GUILayout.Label("Missing Script Finder", EditorStyles.boldLabel);
        GUILayout.Space(10);

        if (GUILayout.Button("Scan Scene for Missing Scripts"))
        {
            ScanScene();
        }

        if (objectsWithMissingScripts.Count > 0)
        {
            GUILayout.Label($"Found {objectsWithMissingScripts.Count} objects with missing scripts:", EditorStyles.boldLabel);
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            for (int i = 0; i < objectsWithMissingScripts.Count; i++)
            {
                if (objectsWithMissingScripts[i] == null) continue;
                
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.ObjectField(objectsWithMissingScripts[i], typeof(GameObject), true);
                
                if (GUILayout.Button("Select", GUILayout.Width(60)))
                {
                    Selection.activeGameObject = objectsWithMissingScripts[i];
                }
                
                if (GUILayout.Button("Remove Missing Scripts", GUILayout.Width(150)))
                {
                    RemoveMissingScripts(objectsWithMissingScripts[i]);
                }
                
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndScrollView();
            
            GUILayout.Space(10);
            if (GUILayout.Button("Remove All Missing Scripts"))
            {
                RemoveAllMissingScripts();
            }
        }
    }

    private void ScanScene()
    {
        objectsWithMissingScripts.Clear();
        
        // Find all GameObjects in the scene
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        
        foreach (GameObject obj in allObjects)
        {
            if (HasMissingScript(obj))
            {
                objectsWithMissingScripts.Add(obj);
            }
        }
        
        Debug.Log($"Missing Script Finder: Found {objectsWithMissingScripts.Count} objects with missing scripts");
    }

    private bool HasMissingScript(GameObject obj)
    {
        Component[] components = obj.GetComponents<Component>();
        
        foreach (Component component in components)
        {
            if (component == null)
            {
                return true;
            }
        }
        
        return false;
    }

    private void RemoveMissingScripts(GameObject obj)
    {
        SerializedObject serializedObject = new SerializedObject(obj);
        SerializedProperty prop = serializedObject.FindProperty("m_Component");
        
        int r = 0;
        for (int j = 0; j < prop.arraySize; j++)
        {
            if (prop.GetArrayElementAtIndex(j).objectReferenceValue == null)
            {
                prop.DeleteArrayElementAtIndex(j);
                r++;
                j--;
            }
        }
        
        serializedObject.ApplyModifiedProperties();
        
        if (r > 0)
        {
            Debug.Log($"Removed {r} missing script(s) from {obj.name}", obj);
        }
    }

    private void RemoveAllMissingScripts()
    {
        foreach (GameObject obj in objectsWithMissingScripts)
        {
            if (obj != null)
            {
                RemoveMissingScripts(obj);
            }
        }
        
        // Rescan after fixing
        ScanScene();
    }
}
#endif