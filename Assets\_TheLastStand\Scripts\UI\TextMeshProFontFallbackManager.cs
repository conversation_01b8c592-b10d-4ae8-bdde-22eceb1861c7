using UnityEngine;
using TMPro;
using System.Collections.Generic;

/// <summary>
/// Manages TextMeshPro font fallbacks to handle Unicode characters that are missing from the primary font.
/// This helps resolve warnings about missing Unicode characters like Korean characters (\uC637).
/// </summary>
public class TextMeshProFontFallbackManager : MonoBehaviour
{
    [Header("Font Fallback Configuration")]
    [SerializeField] private TMP_FontAsset primaryFont;
    [SerializeField] private List<TMP_FontAsset> fallbackFonts = new List<TMP_FontAsset>();
    
    [Header("Auto-Setup Options")]
    [SerializeField] private bool autoSetupOnAwake = true;
    [SerializeField] private bool logMissingCharacters = true;
    
    private static TextMeshProFontFallbackManager instance;
    public static TextMeshProFontFallbackManager Instance => instance;

    private void Awake()
    {
        // Singleton pattern
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            
            if (autoSetupOnAwake)
            {
                SetupFontFallbacks();
            }
        }
        else
        {
            Destroy(gameObject);
        }
    }

    /// <summary>
    /// Sets up font fallbacks for the primary font to handle missing Unicode characters.
    /// </summary>
    public void SetupFontFallbacks()
    {
        if (primaryFont == null)
        {
            // Try to find LiberationSans SDF as it's mentioned in the error
            primaryFont = Resources.Load<TMP_FontAsset>("LiberationSans SDF");
            
            if (primaryFont == null)
            {
                Debug.LogWarning("TextMeshProFontFallbackManager: No primary font specified and LiberationSans SDF not found in Resources.");
                return;
            }
        }

        // Clear existing fallbacks to avoid duplicates
        if (primaryFont.fallbackFontAssetTable == null)
        {
            primaryFont.fallbackFontAssetTable = new List<TMP_FontAsset>();
        }
        else
        {
            primaryFont.fallbackFontAssetTable.Clear();
        }

        // Add fallback fonts
        foreach (TMP_FontAsset fallbackFont in fallbackFonts)
        {
            if (fallbackFont != null && !primaryFont.fallbackFontAssetTable.Contains(fallbackFont))
            {
                primaryFont.fallbackFontAssetTable.Add(fallbackFont);
                Debug.Log($"TextMeshProFontFallbackManager: Added fallback font '{fallbackFont.name}' to '{primaryFont.name}'.");
            }
        }

        // Try to load common Unicode fonts as fallbacks if none are specified
        if (fallbackFonts.Count == 0)
        {
            LoadDefaultFallbackFonts();
        }

        Debug.Log($"TextMeshProFontFallbackManager: Font fallback setup complete for '{primaryFont.name}' with {primaryFont.fallbackFontAssetTable.Count} fallback fonts.");
    }

    /// <summary>
    /// Attempts to load default fallback fonts that support common Unicode characters.
    /// </summary>
    private void LoadDefaultFallbackFonts()
    {
        // Common font names that might support Unicode characters
        string[] defaultFallbackNames = {
            "Arial SDF",
            "NotoSans-Regular SDF", 
            "NotoSansCJK-Regular SDF",
            "DejaVuSans SDF"
        };

        foreach (string fontName in defaultFallbackNames)
        {
            TMP_FontAsset fallbackFont = Resources.Load<TMP_FontAsset>(fontName);
            if (fallbackFont != null)
            {
                primaryFont.fallbackFontAssetTable.Add(fallbackFont);
                Debug.Log($"TextMeshProFontFallbackManager: Added default fallback font '{fontName}'.");
            }
        }
    }

    /// <summary>
    /// Adds a fallback font to the primary font at runtime.
    /// </summary>
    /// <param name="fallbackFont">The fallback font to add</param>
    public void AddFallbackFont(TMP_FontAsset fallbackFont)
    {
        if (primaryFont == null || fallbackFont == null)
        {
            Debug.LogWarning("TextMeshProFontFallbackManager: Cannot add fallback font - primary font or fallback font is null.");
            return;
        }

        if (primaryFont.fallbackFontAssetTable == null)
        {
            primaryFont.fallbackFontAssetTable = new List<TMP_FontAsset>();
        }

        if (!primaryFont.fallbackFontAssetTable.Contains(fallbackFont))
        {
            primaryFont.fallbackFontAssetTable.Add(fallbackFont);
            Debug.Log($"TextMeshProFontFallbackManager: Added fallback font '{fallbackFont.name}' to '{primaryFont.name}'.");
        }
        else
        {
            Debug.LogWarning($"TextMeshProFontFallbackManager: Fallback font '{fallbackFont.name}' is already added to '{primaryFont.name}'.");
        }
    }

    /// <summary>
    /// Logs information about missing characters if logging is enabled.
    /// This can be called from TextMeshPro components when characters are missing.
    /// </summary>
    /// <param name="missingCharacter">The Unicode value of the missing character</param>
    /// <param name="textComponent">The TextMeshPro component that has the missing character</param>
    public void LogMissingCharacter(uint missingCharacter, TMP_Text textComponent)
    {
        if (!logMissingCharacters) return;

        string characterInfo = $"\\u{missingCharacter:X4}";
        if (missingCharacter <= 0xFFFF)
        {
            characterInfo += $" ('{(char)missingCharacter}')";
        }

        Debug.LogWarning($"TextMeshProFontFallbackManager: Missing character {characterInfo} in font '{primaryFont?.name}' for text component '{textComponent?.name}'. Consider adding a fallback font that supports this character.", textComponent);
    }

    /// <summary>
    /// Validates the current font fallback setup and reports any issues.
    /// </summary>
    [ContextMenu("Validate Font Fallback Setup")]
    public void ValidateFontFallbackSetup()
    {
        if (primaryFont == null)
        {
            Debug.LogError("TextMeshProFontFallbackManager: No primary font assigned.");
            return;
        }

        Debug.Log($"TextMeshProFontFallbackManager: Primary font: '{primaryFont.name}'");
        
        if (primaryFont.fallbackFontAssetTable == null || primaryFont.fallbackFontAssetTable.Count == 0)
        {
            Debug.LogWarning("TextMeshProFontFallbackManager: No fallback fonts configured. Unicode characters may not display correctly.");
        }
        else
        {
            Debug.Log($"TextMeshProFontFallbackManager: {primaryFont.fallbackFontAssetTable.Count} fallback fonts configured:");
            for (int i = 0; i < primaryFont.fallbackFontAssetTable.Count; i++)
            {
                var fallback = primaryFont.fallbackFontAssetTable[i];
                if (fallback != null)
                {
                    Debug.Log($"  {i + 1}. {fallback.name}");
                }
                else
                {
                    Debug.LogWarning($"  {i + 1}. [NULL REFERENCE]");
                }
            }
        }
    }
}
