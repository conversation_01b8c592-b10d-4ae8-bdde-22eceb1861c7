using UnityEngine;


public class ForestMap_UIManager : MonoBehaviour
{
    [SerializeField] private CanvasGroup settingsPanel;
    public void Start()
    {
        // TODO: Implement UI Manager functionality
    }

    public void ToggleSettingsPanel()
    {
      settingsPanel.alpha = settingsPanel.alpha > 0 ? 0 : 1;
      settingsPanel.blocksRaycasts = !settingsPanel.blocksRaycasts;
      settingsPanel.interactable = !settingsPanel.interactable;
    }
}
