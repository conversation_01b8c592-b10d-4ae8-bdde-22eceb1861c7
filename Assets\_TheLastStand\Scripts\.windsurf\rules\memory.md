---
trigger: always_on
---

1) Be concise in your explanations
2) Always use as much iterations as possible to fix an error, meaning never stop looping unless I intervene.
3) Be as autonomous as possible by making thoughtful decisions and utilizing the tools at your disposal to efficiently do that.

Codebase Structure main notes:

/ParentClasses is a folder for shared/base classes. This is meant to utilize object oriented programming principles and game programming patterns

/PersistentData is any data that will need to be persistent in all levels at all times

/ForestMap holds all code related to the forest map.

Keep in mind that this codebase was started from a multiplayer template, therefore some files may or may not be used. So it is crucial to understand the flow of logic.