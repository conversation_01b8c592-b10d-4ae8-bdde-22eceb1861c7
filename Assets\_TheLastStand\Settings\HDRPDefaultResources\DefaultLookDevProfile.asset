%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: DefaultLookDevProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 8761387877531654226}
  - {fileID: 1902828633788537306}
  - {fileID: 1880163708194025631}
  - {fileID: 2340290907100754200}
  - {fileID: 6859032730710831169}
--- !u!114 &1880163708194025631
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9008a067f4d626c4d8bc4bc48f04bb89, type: 3}
  m_Name: AmbientOcclusion
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 0
    m_Value: 1
  rayTracing:
    m_OverrideState: 0
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.5
  directLightingStrength:
    m_OverrideState: 0
    m_Value: 0
  radius:
    m_OverrideState: 1
    m_Value: 1
  spatialBilateralAggressiveness:
    m_OverrideState: 0
    m_Value: 0.15
  temporalAccumulation:
    m_OverrideState: 0
    m_Value: 1
  ghostingReduction:
    m_OverrideState: 0
    m_Value: 0.5
  blurSharpness:
    m_OverrideState: 0
    m_Value: 0.1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  specularOcclusion:
    m_OverrideState: 0
    m_Value: 0.5
  occluderMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  receiverMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  m_StepCount:
    m_OverrideState: 0
    m_Value: 6
  m_FullResolution:
    m_OverrideState: 0
    m_Value: 0
  m_MaximumRadiusInPixels:
    m_OverrideState: 0
    m_Value: 40
  m_BilateralUpsample:
    m_OverrideState: 0
    m_Value: 1
  m_DirectionCount:
    m_OverrideState: 0
    m_Value: 2
  m_RayLength:
    m_OverrideState: 0
    m_Value: 3
  m_SampleCount:
    m_OverrideState: 0
    m_Value: 2
  m_Denoise:
    m_OverrideState: 0
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 0
    m_Value: 0.5
--- !u!114 &1902828633788537306
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7ddcec8a8eb2d684d833ac8f5d26aebd, type: 3}
  m_Name: HDShadowSettings
  m_EditorClassIdentifier: 
  active: 1
  interCascadeBorders: 1
  maxShadowDistance:
    m_OverrideState: 1
    m_Value: 25
  directionalTransmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
  cascadeShadowSplitCount:
    m_OverrideState: 1
    m_Value: 2
  cascadeShadowSplit0:
    m_OverrideState: 0
    m_Value: 0.05
  cascadeShadowSplit1:
    m_OverrideState: 0
    m_Value: 0.15
  cascadeShadowSplit2:
    m_OverrideState: 0
    m_Value: 0.3
  cascadeShadowBorder0:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder1:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder2:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder3:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &2340290907100754200
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24f077503be6ae942a1e1245dbd53ea9, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 0
    m_Value: 3
  threshold:
    m_OverrideState: 0
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.1
  scatter:
    m_OverrideState: 0
    m_Value: 0.7
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
  anamorphic:
    m_OverrideState: 0
    m_Value: 1
  m_Resolution:
    m_OverrideState: 0
    m_Value: 2
  m_HighQualityPrefiltering:
    m_OverrideState: 0
    m_Value: 0
  m_HighQualityFiltering:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &6859032730710831169
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a7ff42a8c5be6646ad3975f3a54c1eb, type: 3}
  m_Name: DiffusionProfileList
  m_EditorClassIdentifier: 
  active: 1
  diffusionProfiles:
    m_OverrideState: 1
    m_Value:
    - {fileID: 11400000, guid: 2b7005ba3a4d8474b8cdc34141ad766e, type: 2}
    - {fileID: 11400000, guid: c76dfec79442f6b41a0ebdde5394f0bc, type: 2}
--- !u!114 &8761387877531654226
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f086a068d4c5889438831b3ae9afc11c, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 1
  useFullACES:
    m_OverrideState: 0
    m_Value: 0
  toeStrength:
    m_OverrideState: 0
    m_Value: 0
  toeLength:
    m_OverrideState: 0
    m_Value: 0.5
  shoulderStrength:
    m_OverrideState: 0
    m_Value: 0
  shoulderLength:
    m_OverrideState: 0
    m_Value: 0.5
  shoulderAngle:
    m_OverrideState: 0
    m_Value: 0
  gamma:
    m_OverrideState: 0
    m_Value: 1
  lutTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  lutContribution:
    m_OverrideState: 0
    m_Value: 1
  neutralHDRRangeReductionMode:
    m_OverrideState: 0
    m_Value: 2
  acesPreset:
    m_OverrideState: 0
    m_Value: 3
  fallbackMode:
    m_OverrideState: 0
    m_Value: 1
  hueShiftAmount:
    m_OverrideState: 0
    m_Value: 0
  detectPaperWhite:
    m_OverrideState: 0
    m_Value: 0
  paperWhite:
    m_OverrideState: 0
    m_Value: 300
  detectBrightnessLimits:
    m_OverrideState: 0
    m_Value: 1
  minNits:
    m_OverrideState: 0
    m_Value: 0.005
  maxNits:
    m_OverrideState: 0
    m_Value: 1000
