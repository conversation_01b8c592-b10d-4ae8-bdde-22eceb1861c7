# Helicopter Transform Constraint System

## Overview

The Helicopter Transform Constraint System maintains proper player positioning and controlled rotation during helicopter flight sequences while allowing social interaction through limited head movement.

## Architecture

### Core Components

#### HelicopterTransformConstraint
**Location:** `Assets/_TheLastStand/Scripts/ForestMap/HelicopterTransformConstraint.cs`

Individual player constraint component that enforces position and rotation limits.

**Key Features:**
- Position locking to (0,0,0) relative to spawn point parent
- Selective rotation constraints (lock X/Z axes, allow Y-axis)
- Smooth constraint enforcement to prevent jarring corrections
- Network synchronization via Mirror
- Visual feedback and debugging tools

**Configuration:**
```csharp
[Header("Position Constraints")]
[SerializeField] private bool constrainPosition = true;
[SerializeField] private Vector3 constrainedLocalPosition = Vector3.zero;

[Header("Rotation Constraints")]
[SerializeField] private bool constrainRotation = true;
[SerializeField] private bool lockXRotation = true;    // Prevent pitch
[SerializeField] private bool lockZRotation = true;    // Prevent roll
[SerializeField] private bool allowYRotation = true;   // Allow yaw for social interaction
[SerializeField] private float yRotationMin = -90f;   // Left limit
[SerializeField] private float yRotationMax = 90f;    // Right limit
```

#### HelicopterConstraintManager
**Location:** `Assets/_TheLastStand/Scripts/Managers/HelicopterConstraintManager.cs`

Scene-level manager that coordinates constraint activation based on helicopter flight state.

**Key Features:**
- Automatic constraint activation during helicopter flight
- Integration with helicopter state machine
- Centralized player registration and management
- Network-synchronized global constraint state

**State Management:**
- **Flight States** → Constraints Active
- **Landing/PathComplete** → Constraints Deactivated
- **Exit Sequence** → Constraints Deactivated

## Integration Points

### ForestPlayerManager Integration
Automatically adds `HelicopterTransformConstraint` components to spawned networked players:

```csharp
// Add HelicopterTransformConstraint component for helicopter flight constraints
HelicopterTransformConstraint constraint = networkedPlayer.GetComponent<HelicopterTransformConstraint>();
if (constraint == null)
{
    constraint = networkedPlayer.AddComponent<HelicopterTransformConstraint>();
}
```

### Player Look System Integration
Both `ForestIntroPlayer` and `ForestPlayer` respect constraints during mouse look processing:

```csharp
// Apply Y-axis rotation (yaw) with constraint checking
if (helicopterConstraint != null && helicopterConstraint.ConstraintsActive)
{
    float currentY = transform.localEulerAngles.y;
    float newY = currentY + mouseX;
    float clampedY = helicopterConstraint.ClampYRotation(newY);
    
    Vector3 currentEuler = transform.localEulerAngles;
    currentEuler.y = clampedY;
    transform.localEulerAngles = currentEuler;
}
```

### Helicopter Exit Integration
`HelicopterExitManager` notifies the constraint system when exit sequences begin:

```csharp
// Notify constraint manager about exit sequence start
HelicopterConstraintManager constraintManager = HelicopterConstraintManager.Instance;
if (constraintManager != null)
{
    constraintManager.OnHelicopterExitSequenceStarted();
}
```

## Constraint Behavior

### Position Constraints
- **Target:** Local position (0,0,0) relative to spawn point parent
- **Enforcement:** Smooth lerping with configurable smoothness factor
- **Purpose:** Keeps players properly seated in helicopter during flight

### Rotation Constraints
- **X-Axis (Pitch):** Locked to 0° - prevents looking up/down beyond helicopter interior
- **Z-Axis (Roll):** Locked to 0° - prevents tilting that would break immersion
- **Y-Axis (Yaw):** Configurable range (default ±90°) - allows turning to look at other players

### Constraint Activation Timeline
1. **Player Spawning:** Constraint component added automatically
2. **Helicopter Flight Start:** Constraints activated via state monitoring
3. **Helicopter Landing:** Constraints deactivated (configurable)
4. **Exit Sequence:** Constraints deactivated for player movement
5. **Free Movement:** Normal player controls restored

## Network Synchronization

The system uses Mirror's `[SyncVar]` attributes to ensure constraint states are synchronized across all clients:

```csharp
[SyncVar(hook = nameof(OnConstraintsActiveChanged))]
private bool networkConstraintsActive = false;

[SyncVar(hook = nameof(OnGlobalConstraintsChanged))]
private bool networkGlobalConstraintsActive = false;
```

## Configuration Options

### Per-Player Constraints (HelicopterTransformConstraint)
- Position constraint toggle and target position
- Individual rotation axis locks
- Y-rotation range limits for social interaction
- Constraint smoothness factor
- Visual feedback and debugging options

### Global Management (HelicopterConstraintManager)
- Automatic activation during flight phases
- Timing delays for activation/deactivation
- Integration toggles for landing and exit sequences
- Debug logging controls

## Usage Examples

### Manual Constraint Control
```csharp
// Get constraint manager
HelicopterConstraintManager manager = HelicopterConstraintManager.Instance;

// Force activate constraints for all players
manager.ForceActivateConstraints();

// Force deactivate constraints for all players
manager.ForceDeactivateConstraints();

// Check constraint status
string status = manager.GetConstraintStatus();
```

### Individual Player Constraint Control
```csharp
// Get player's constraint component
HelicopterTransformConstraint constraint = player.GetComponent<HelicopterTransformConstraint>();

// Check if constraints are active
bool isConstrained = constraint.ConstraintsActive;

// Check if Y rotation is within limits
bool withinLimits = constraint.IsYRotationWithinLimits(yRotation);

// Clamp Y rotation to allowed range
float clampedY = constraint.ClampYRotation(yRotation);
```

## Debugging and Visualization

### Debug Information
- Enable `showDebugInfo` for real-time constraint status logging
- Use `GetConstraintStatus()` for system-wide status information
- Visual gizmos show constraint boundaries in Scene view

### Common Issues
- **Constraints not activating:** Check helicopter reference in HelicopterConstraintManager
- **Jerky movement:** Adjust `constraintSmoothness` value
- **Rotation limits too restrictive:** Modify `yRotationMin/Max` values
- **Network desync:** Ensure proper Mirror networking setup

## Performance Considerations

- Constraint enforcement runs only for local players (`isLocalPlayer`)
- Smooth constraint application uses `Time.deltaTime` for frame-rate independence
- Network synchronization minimizes bandwidth with `[SyncVar]` hooks
- Component registration/unregistration handled automatically

## Future Enhancements

- **Visual Feedback:** UI indicators for rotation limits
- **Audio Cues:** Sound feedback when hitting rotation limits
- **Customizable Profiles:** Different constraint sets for different helicopter types
- **Animation Integration:** Smooth transitions between constrained and free states
