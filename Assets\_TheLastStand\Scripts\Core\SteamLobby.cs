using Mirror;
using Steamworks;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

[System.Serializable]
public class Lobby
{
    public CSteamID lobbyID;
    public string name;

    public Lobby(CSteamID lobbyID, string name)
    {
        this.lobbyID = lobbyID;
        this.name = name;
    }
}

public class SteamLobby : MonoBehaviour
{
    private const string HOST_ADDRESS_KEY = "HostAddress";
                      
    public static SteamLobby instance;
    public static CSteamID LobbyID;

    public List<Lobby> allLobbies = new List<Lobby>();
    private bool isRefreshingLobbies = false; // Flag to prevent concurrent refreshes
    private bool findMatchRequested = false; // Flag to indicate FindMatch was clicked
    private float findMatchStartTime = 0f;
    private const float FIND_MATCH_TIMEOUT = 15.0f; // Timeout in seconds

    //CallBacks
    protected Callback<LobbyCreated_t> lobbyCreated;
    protected Callback<GameLobbyJoinRequested_t> joinRequested;
    protected Callback<LobbyEnter_t> lobbyEntered;
    protected Callback<LobbyMatchList_t> lobbyMatchList;


    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject); // Keep SteamLobby across scenes
        }
        else
        {
            Destroy(gameObject); // Destroy duplicate
            return;
        }

        lobbyMatchList = Callback<LobbyMatchList_t>.Create(OnLobbyMatchList);
    }

    private void Start()
    {
        if (!SteamManager.Initialized) 
        {
            Debug.LogError("SteamManager is not initialized!");
            return;
        }

        lobbyCreated = Callback<LobbyCreated_t>.Create(OnLobbyCreated);
        joinRequested = Callback<GameLobbyJoinRequested_t>.Create(OnJoinRequest);
        lobbyEntered = Callback<LobbyEnter_t>.Create(OnLobbyEntered);

        // Don't necessarily reload on start, wait for explicit call or FindMatch
        // ReloadLobbyList(); 
    }


    #region Steam Lobby
    public void ReloadLobbyList()
    {        
        if (isRefreshingLobbies) return; // Prevent spamming requests
        
        isRefreshingLobbies = true;
        Debug.Log("Requesting lobby list refresh...");
        // Clear list before requesting new one
        allLobbies.Clear(); 

        SteamMatchmaking.AddRequestLobbyListDistanceFilter(ELobbyDistanceFilter.k_ELobbyDistanceFilterWorldwide);
        SteamMatchmaking.AddRequestLobbyListStringFilter("displayable", "true", ELobbyComparison.k_ELobbyComparisonEqual);
        // Add filter for non-full lobbies. This might need adjustment based on exact lobby data set.
        // Note: Steam doesn't have a direct "slots available > 0" filter. We filter after getting the list.
        SteamMatchmaking.RequestLobbyList(); // Async request
    }
    
    void OnLobbyMatchList(LobbyMatchList_t param)
    {
        Debug.Log($"OnLobbyMatchList callback received. Found {param.m_nLobbiesMatching} lobbies.");
        isRefreshingLobbies = false; // Reset flag

        // Populate the list
        allLobbies.Clear(); // Clear again here to ensure it reflects the callback results
        for (int i = 0; i < param.m_nLobbiesMatching; i++)
        {                        
            CSteamID lobbyID = SteamMatchmaking.GetLobbyByIndex(i);
            if (lobbyID.IsValid()) 
            {
                string lobbyName = SteamMatchmaking.GetLobbyData(lobbyID, "name");
                int currentMembers = SteamMatchmaking.GetNumLobbyMembers(lobbyID);
                int maxMembers = SteamMatchmaking.GetLobbyMemberLimit(lobbyID);

                Debug.Log($"  Lobby {i}: ID={lobbyID}, Name='{lobbyName}', Members={currentMembers}/{maxMembers}");
                allLobbies.Add(new Lobby(lobbyID, lobbyName));
            }
            else
            {
                Debug.LogWarning($"  Lobby index {i} returned invalid CSteamID.");
            }
        }

        // If FindMatch was requested, try joining now that the list is updated
        if (findMatchRequested)
        {
            TryJoiningFoundLobby();
        }

        // Optional sorting if needed for a UI list elsewhere
        // allLobbies.Sort((a, b) => SteamMatchmaking.GetNumLobbyMembers(b.lobbyID).CompareTo(SteamMatchmaking.GetNumLobbyMembers(a.lobbyID)));
    }


    public void CreateLobby()
    {
        Debug.Log("Attempting to create lobby...");
        SteamMatchmaking.CreateLobby(ELobbyType.k_ELobbyTypePublic, ((MyNetworkManager)NetworkManager.singleton).maxConnections);
    }


    public void JoinLobby(CSteamID lobby)
    {
        Debug.Log($"Attempting to join lobby {lobby}...");
        SteamMatchmaking.JoinLobby(lobby);
    }


    private void OnLobbyCreated(LobbyCreated_t callback)
    {
        findMatchRequested = false; // Stop find match process if we created one
        if (callback.m_eResult != EResult.k_EResultOK) 
        {
            Debug.LogError($"Failed to create lobby. Result: {callback.m_eResult}");
            PopupManager.instance.Popup_Show("Failed to Create Party");

            // Reset UI and network state
            if (MainMenu.instance != null)
            {
                MainMenu.instance.SetMenuState(MenuState.Home);
            }
            if (NetworkManager.singleton is MyNetworkManager myNM)
            {
                myNM.SetMultiplayer(false); // Reset multiplayer state
            }
            // Potentially stop any network activity if something partially started
            if (NetworkManager.singleton.isNetworkActive && NetworkServer.active)
            {
                NetworkManager.singleton.StopHost();
            }
            else if (NetworkManager.singleton.isNetworkActive)
            {
                NetworkManager.singleton.StopClient();
            }

            return; 
        }

        Debug.Log($"Lobby created successfully. ID: {callback.m_ulSteamIDLobby}");
        LobbyID = new CSteamID(callback.m_ulSteamIDLobby);

        // Start host immediately
        ((MyNetworkManager)NetworkManager.singleton).StartHost();

        // Set lobby data
        string lobbyName = SteamFriends.GetPersonaName();
        SteamMatchmaking.SetLobbyData(LobbyID, HOST_ADDRESS_KEY, SteamUser.GetSteamID().ToString());
        SteamMatchmaking.SetLobbyData(LobbyID, "name", lobbyName + "'s Lobby"); // Give a default name
        SteamMatchmaking.SetLobbyData(LobbyID, "displayable", "true");

        // Optional: Set other data like map name, game mode etc.

        // SetLobbyLocation(); // Can be called if needed
    }


    private void OnJoinRequest(GameLobbyJoinRequested_t callback)
    {
        Debug.Log($"Received request to join lobby {callback.m_steamIDLobby} from friend {callback.m_steamIDFriend}.");
        PopupManager.instance.Popup_Show("Joining Friend's Party...");
        JoinLobby(callback.m_steamIDLobby);
    }

    private void OnLobbyEntered(LobbyEnter_t callback)
    {
        findMatchRequested = false; // Stop find match process if we entered one
        LobbyID = new CSteamID(callback.m_ulSteamIDLobby);
        Debug.Log($"Entered lobby {LobbyID}. Response: {callback.m_EChatRoomEnterResponse}");

        if (callback.m_EChatRoomEnterResponse != (uint)EChatRoomEnterResponse.k_EChatRoomEnterResponseSuccess)
        {
            Debug.LogError($"Failed to enter lobby {LobbyID}. Response code: {callback.m_EChatRoomEnterResponse}");
            SteamMatchmaking.LeaveLobby(LobbyID); // Leave immediately if entry failed
            PopupManager.instance.Popup_Show("Failed to Join Party");
            // Reset state if necessary
            if (MainMenu.instance != null) MainMenu.instance.SetMenuState(MenuState.Home);
            return;
        }
        
        //If lobby full or locked, leave (Steam should handle this via response code, but double-check)
        if (callback.m_bLocked) // Note: k_EChatRoomEnterResponse codes cover most cases like Full, DoesNotExist etc.
        {
            Debug.LogWarning($"Lobby {LobbyID} is locked or full. Leaving.");
            SteamMatchmaking.LeaveLobby(LobbyID);
            PopupManager.instance.Popup_Show("Party is Locked or Full");
             if (MainMenu.instance != null) MainMenu.instance.SetMenuState(MenuState.Home);
            return;
        }

        // If we are the host, we don't need to connect as a client
        if (NetworkServer.active)
        {
            Debug.Log("Already server/host, skipping client connection on LobbyEnter.");
            return;
        }

        // We are a client joining a lobby, connect to the host
        string hostAddress = SteamMatchmaking.GetLobbyData(LobbyID, HOST_ADDRESS_KEY);
        if (string.IsNullOrEmpty(hostAddress))
        {
            Debug.LogError($"Failed to get host address from lobby {LobbyID} data!");
            SteamMatchmaking.LeaveLobby(LobbyID);
            PopupManager.instance.Popup_Show("Error Getting Host Info");
            if (MainMenu.instance != null) MainMenu.instance.SetMenuState(MenuState.Home);
            return;
        }
        
        Debug.Log($"Connecting to host at address: {hostAddress}");
        ((MyNetworkManager)NetworkManager.singleton).SetMultiplayer(true);
        ((MyNetworkManager)NetworkManager.singleton).networkAddress = hostAddress;
        ((MyNetworkManager)NetworkManager.singleton).StartClient();
        // Popup will likely be closed by NetworkManager's OnStartClient
    }
    #endregion

    public void Leave()
    {
        if (LobbyID.IsValid())
        {
             Debug.Log($"Leaving lobby {LobbyID}.");
             SteamMatchmaking.LeaveLobby(LobbyID);
             LobbyID = CSteamID.Nil;
        }
        findMatchRequested = false; // Stop searching if we leave
    }


    public static void SetLobbyLocation()
    {
        if (!LobbyID.IsValid()) return;
        SteamNetworkingUtils.GetLocalPingLocation(out SteamNetworkPingLocation_t pingLocation);
        SteamNetworkingUtils.ConvertPingLocationToString(ref pingLocation, out string result, 1024);
        SteamMatchmaking.SetLobbyData(LobbyID, "location", result);
    }

    public void FindMatch() 
    {
        if (findMatchRequested || isRefreshingLobbies) return; // Prevent starting multiple searches
        
        Debug.Log("Find Match clicked.");
        findMatchRequested = true;
        findMatchStartTime = Time.time;
        PopupManager.instance.Popup_Show("Finding Match...");
        ReloadLobbyList(); // Start the process by refreshing the lobby list
    }

    private void TryJoiningFoundLobby()
    {
        Debug.Log("Trying to join a found lobby...");
        bool joined = false;
        foreach (Lobby lobby in allLobbies)
        {
            if (lobby.lobbyID.IsValid())
            {
                int currentMembers = SteamMatchmaking.GetNumLobbyMembers(lobby.lobbyID);
                int maxMembers = SteamMatchmaking.GetLobbyMemberLimit(lobby.lobbyID);
                
                // Ensure maxMembers is valid (might be 0 if data not fully available yet)
                if (maxMembers <= 0) maxMembers = NetworkManager.singleton.maxConnections; 

                if (currentMembers > 0 && currentMembers < maxMembers) 
                {
                    Debug.Log($"Found suitable lobby: {lobby.name} ({currentMembers}/{maxMembers}). Joining...");
                    JoinLobby(lobby.lobbyID);
                    joined = true;
                    findMatchRequested = false; // No longer searching
                    // Popup will be closed by OnLobbyEntered or NetworkManager OnStartClient
                    break; // Exit loop once joined
                }
            }
        }

        // If FindMatch was requested and still hasn't joined after checking list
        if (findMatchRequested && !joined)
        {
            // Check timeout
            if (Time.time - findMatchStartTime > FIND_MATCH_TIMEOUT)
            {
                Debug.Log("Find Match timed out. Creating a new lobby.");
                PopupManager.instance.Popup_Show("Creating Party..."); 
                CreateLobby();
                findMatchRequested = false; // Stop searching
            }
            else
            {   
                // No suitable lobby found yet, but not timed out. Request another refresh.
                Debug.Log("No suitable lobby found yet. Refreshing list again...");
                // Optional: Add a small delay before refreshing again?
                // StartCoroutine(DelayedListRefresh());
                 ReloadLobbyList(); // Or just refresh immediately
            }
        }
    }
    
    /* Optional helper for delayed refresh
    private IEnumerator DelayedListRefresh()
    {
        yield return new WaitForSeconds(2.0f); // Wait 2 seconds
        if (findMatchRequested) // Check if still searching
        {
            ReloadLobbyList();
        }
    }
    */
}
                   